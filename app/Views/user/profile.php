<?= $this->extend('layouts/user') ?>

<?= $this->section('title') ?>Profile<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Profile Header -->
<div class="user-card mb-4">
    <div class="user-card-header text-center">
        <div class="profile-avatar">
            <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
        </div>
        <h2><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></h2>
        <p class="mb-0">@<?= esc($user['username']) ?></p>
        <span class="badge bg-light text-dark mt-2"><?= ucfirst($user['role']) ?></span>
    </div>
</div>
                
<!-- Profile Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="user-card">
            <div class="user-card-body">
                <h5 class="mb-4"><i class="fas fa-edit me-2"></i>Edit Profile</h5>

                <form action="<?= base_url('user/profile') ?>" method="post">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="first_name" name="first_name"
                                   value="<?= old('first_name', $user['first_name']) ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="last_name" name="last_name"
                                   value="<?= old('last_name', $user['last_name']) ?>" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email"
                               value="<?= old('email', $user['email']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="bio" class="form-label">Bio</label>
                        <textarea class="form-control" id="bio" name="bio" rows="4"
                                  placeholder="Tell us about yourself..."><?= old('bio', $user['bio'] ?? '') ?></textarea>
                        <div class="form-text">Maximum 500 characters</div>
                    </div>

                    <hr class="my-4">

                    <h6>Change Password</h6>
                    <p class="text-muted small">Leave blank to keep current password</p>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="password" name="password">
                            <div class="form-text">Minimum 6 characters</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="password_confirm" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="password_confirm" name="password_confirm">
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('user/dashboard') ?>" class="btn btn-user-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-user-primary">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="user-card">
            <div class="user-card-body">
                <h6 class="mb-4"><i class="fas fa-info-circle me-2"></i>Account Information</h6>

                <div class="mb-3">
                    <strong>Username:</strong>
                    <p class="mb-0 text-muted">@<?= esc($user['username']) ?></p>
                </div>

                <div class="mb-3">
                    <strong>Role:</strong>
                    <p class="mb-0">
                        <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'editor' ? 'warning' : ($user['role'] === 'author' ? 'info' : 'secondary')) ?>">
                            <?= ucfirst($user['role']) ?>
                        </span>
                    </p>
                </div>

                <div class="mb-3">
                    <strong>Status:</strong>
                    <p class="mb-0">
                        <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'secondary' ?>">
                            <?= ucfirst($user['status']) ?>
                        </span>
                    </p>
                </div>

                <div class="mb-3">
                    <strong>Email Verified:</strong>
                    <p class="mb-0">
                        <?php if (!empty($user['email_verified_at'])): ?>
                            <i class="fas fa-check-circle text-success me-1"></i>Verified on <?= date('M j, Y', strtotime($user['email_verified_at'])) ?>
                        <?php else: ?>
                            <i class="fas fa-exclamation-circle text-warning me-1"></i>Not verified
                        <?php endif; ?>
                    </p>
                </div>

                <div class="mb-3">
                    <strong>Member Since:</strong>
                    <p class="mb-0 text-muted"><?= date('F j, Y', strtotime($user['created_at'])) ?></p>
                </div>

                <?php if ($user['last_login']): ?>
                <div class="mb-0">
                    <strong>Last Login:</strong>
                    <p class="mb-0 text-muted"><?= date('M j, Y g:i A', strtotime($user['last_login'])) ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="user-card mt-3">
            <div class="user-card-body">
                <h6 class="mb-3"><i class="fas fa-shield-alt me-2"></i>Security Tips</h6>
                <ul class="small mb-0">
                    <li>Use a strong, unique password</li>
                    <li>Keep your email address up to date</li>
                    <li>Log out from shared computers</li>
                    <li>Review your account activity regularly</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Password confirmation validation
    document.getElementById('password_confirm').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;

        if (password && confirmPassword && password !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });

    // Bio character counter
    const bioTextarea = document.getElementById('bio');
    const maxLength = 500;

    bioTextarea.addEventListener('input', function() {
        const remaining = maxLength - this.value.length;
        const formText = this.nextElementSibling;

        if (remaining < 0) {
            formText.textContent = `${Math.abs(remaining)} characters over limit`;
            formText.className = 'form-text text-danger';
            this.classList.add('is-invalid');
        } else {
            formText.textContent = `${remaining} characters remaining`;
            formText.className = 'form-text text-muted';
            this.classList.remove('is-invalid');
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('password_confirm').value;

        if (password && password !== confirmPassword) {
            e.preventDefault();
            document.getElementById('password_confirm').classList.add('is-invalid');
            alert('Passwords do not match');
        }
    });
</script>
<?= $this->endSection() ?>
