<?= $this->extend('layouts/user') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Welcome Section -->
<div class="user-card user-card-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2>Welcome back, <?= esc($user['first_name']) ?>!</h2>
            <p class="mb-0">Here's what's happening with your account today.</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex align-items-center justify-content-end">
                <div class="bg-white bg-opacity-20 rounded-circle p-3">
                    <i class="fas fa-user fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Statistics Cards -->
<div class="row mb-4">
    <?php if (in_array($user['role'], ['admin', 'editor', 'author'])): ?>
    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-icon bg-user-primary">
                <i class="fas fa-blog"></i>
            </div>
            <div class="stat-value"><?= $stats['total_posts'] ?></div>
            <div class="stat-label">Blog Posts</div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-icon bg-success">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-value"><?= $stats['total_pages'] ?></div>
            <div class="stat-label">Pages</div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-icon bg-info">
                <i class="fas fa-images"></i>
            </div>
            <div class="stat-value"><?= $stats['total_media'] ?></div>
            <div class="stat-label">Media Files</div>
        </div>
    </div>
    <?php endif; ?>

    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-icon bg-warning">
                <i class="fas fa-comments"></i>
            </div>
            <div class="stat-value"><?= $stats['total_comments'] ?></div>
            <div class="stat-label">Comments</div>
        </div>
    </div>
</div>
<!-- Recent Activity -->
<div class="row">
    <?php if (in_array($user['role'], ['admin', 'editor', 'author']) && !empty($recent_posts)): ?>
    <div class="col-md-6 mb-4">
        <div class="user-card">
            <div class="user-card-body">
                <h5 class="mb-4"><i class="fas fa-blog me-2"></i>Recent Blog Posts</h5>

                <?php foreach ($recent_posts as $post): ?>
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-user-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 40px; height: 40px;">
                            <i class="fas fa-blog"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">
                            <a href="<?= base_url('blog/' . $post['slug']) ?>" class="text-decoration-none">
                                <?= esc($post['title']) ?>
                            </a>
                        </h6>
                        <small class="text-muted">
                            <?= date('M j, Y', strtotime($post['created_at'])) ?> •
                            <span class="badge bg-<?= $post['status'] === 'published' ? 'success' : 'warning' ?>">
                                <?= ucfirst($post['status']) ?>
                            </span>
                        </small>
                    </div>
                </div>
                <?php endforeach; ?>

                <div class="text-center">
                    <a href="<?= base_url('user/posts') ?>" class="btn btn-user-primary btn-sm">
                        View All Posts
                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if (in_array($user['role'], ['admin', 'editor', 'author']) && !empty($recent_pages)): ?>
    <div class="col-md-6 mb-4">
        <div class="user-card">
            <div class="user-card-body">
                <h5 class="mb-4"><i class="fas fa-file-alt me-2"></i>Recent Pages</h5>

                <?php foreach ($recent_pages as $page): ?>
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 40px; height: 40px;">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">
                            <a href="<?= base_url('page/' . $page['slug']) ?>" class="text-decoration-none">
                                <?= esc($page['title']) ?>
                            </a>
                        </h6>
                        <small class="text-muted">
                            <?= date('M j, Y', strtotime($page['created_at'])) ?> •
                            <span class="badge bg-<?= $page['status'] === 'published' ? 'success' : 'warning' ?>">
                                <?= ucfirst($page['status']) ?>
                            </span>
                        </small>
                    </div>
                </div>
                <?php endforeach; ?>

                <div class="text-center">
                    <a href="<?= base_url('user/pages') ?>" class="btn btn-user-secondary btn-sm">
                        View All Pages
                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="user-card">
            <div class="user-card-body">
                <h5 class="mb-4"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>

                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('user/profile') ?>" class="btn btn-user-primary w-100">
                            <i class="fas fa-user me-2"></i>Edit Profile
                        </a>
                    </div>

                    <?php if (in_array($user['role'], ['admin', 'editor', 'author'])): ?>
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/blog/create') ?>" class="btn btn-user-secondary w-100">
                            <i class="fas fa-plus me-2"></i>New Post
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/pages/create') ?>" class="btn btn-user-secondary w-100">
                            <i class="fas fa-file-plus me-2"></i>New Page
                        </a>
                    </div>
                    <?php endif; ?>

                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('user/settings') ?>" class="btn btn-user-secondary w-100">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
