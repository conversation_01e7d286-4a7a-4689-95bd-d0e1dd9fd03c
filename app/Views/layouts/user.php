<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->renderSection('title', true) ?> - CMS</title>
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- User Dashboard Styles -->
    <style>
        :root {
            --user-primary: #667eea;
            --user-primary-dark: #5a6fd8;
            --user-secondary: #764ba2;
            --user-light: #f8f9fa;
            --user-dark: #343a40;
            --user-border: #dee2e6;
            --user-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --user-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        body {
            background: var(--user-light);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Navigation */
        .user-navbar {
            background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-secondary) 100%);
            box-shadow: var(--user-shadow-lg);
        }

        .user-navbar .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .user-navbar .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            transition: color 0.3s ease;
        }

        .user-navbar .nav-link:hover {
            color: white !important;
        }

        .user-navbar .dropdown-menu {
            border: none;
            box-shadow: var(--user-shadow-lg);
            border-radius: 8px;
        }

        /* Sidebar */
        .user-sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: var(--user-shadow);
            border-radius: 12px;
            margin-top: 20px;
        }

        .user-sidebar .nav-link {
            color: var(--user-dark);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .user-sidebar .nav-link:hover {
            background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-secondary) 100%);
            color: white;
            transform: translateX(4px);
        }

        .user-sidebar .nav-link.active {
            background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-secondary) 100%);
            color: white;
        }

        .user-sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .user-main {
            padding: 20px;
        }

        /* Cards */
        .user-card {
            background: white;
            border-radius: 12px;
            box-shadow: var(--user-shadow);
            border: 1px solid var(--user-border);
            overflow: hidden;
        }

        .user-card-header {
            background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-secondary) 100%);
            color: white;
            padding: 20px 24px;
            border-bottom: none;
        }

        .user-card-body {
            padding: 24px;
        }

        .user-card-footer {
            background: #f8f9fa;
            padding: 16px 24px;
            border-top: 1px solid var(--user-border);
        }

        /* Profile Avatar */
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            font-weight: 700;
            margin: 0 auto 20px;
            border: 4px solid rgba(255, 255, 255, 0.3);
        }

        /* Forms */
        .form-control:focus {
            border-color: var(--user-primary);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: var(--user-dark);
            margin-bottom: 8px;
        }

        /* Buttons */
        .btn-user-primary {
            background: linear-gradient(135deg, var(--user-primary) 0%, var(--user-secondary) 100%);
            border: none;
            color: white;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-user-primary:hover {
            background: linear-gradient(135deg, var(--user-primary-dark) 0%, #6a4190 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--user-shadow-lg);
        }

        .btn-user-secondary {
            background: white;
            border: 1px solid var(--user-border);
            color: var(--user-dark);
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-user-secondary:hover {
            background: var(--user-light);
            border-color: var(--user-primary);
            color: var(--user-primary);
        }

        /* Alerts */
        .alert {
            border: none;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-success {
            background: rgba(25, 135, 84, 0.1);
            border-left-color: #198754;
            color: #198754;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            border-left-color: #dc3545;
            color: #dc3545;
        }

        .alert-warning {
            background: rgba(255, 193, 7, 0.1);
            border-left-color: #ffc107;
            color: #856404;
        }

        .alert-info {
            background: rgba(13, 202, 240, 0.1);
            border-left-color: #0dcaf0;
            color: #055160;
        }

        /* Badges */
        .badge {
            font-weight: 500;
            padding: 6px 12px;
            border-radius: 6px;
        }

        /* Stats Cards */
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--user-shadow);
            border: 1px solid var(--user-border);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--user-shadow-lg);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--user-primary);
            margin-bottom: 8px;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin: 0 auto 16px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .user-sidebar {
                margin-top: 10px;
                margin-bottom: 20px;
            }

            .user-main {
                padding: 15px;
            }

            .user-card-body {
                padding: 20px;
            }

            .profile-avatar {
                width: 80px;
                height: 80px;
                font-size: 2.5rem;
            }
        }

        /* Utilities */
        .text-user-primary { color: var(--user-primary) !important; }
        .text-user-secondary { color: var(--user-secondary) !important; }
        .bg-user-primary { background-color: var(--user-primary) !important; }
        .bg-user-secondary { background-color: var(--user-secondary) !important; }
        .border-user { border-color: var(--user-border) !important; }
        .shadow-user { box-shadow: var(--user-shadow) !important; }
        .shadow-user-lg { box-shadow: var(--user-shadow-lg) !important; }
    </style>
    
    <?= $this->renderSection('head') ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark user-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <i class="fas fa-home me-2"></i>CMS
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url() ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('blog') ?>">Blog</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" 
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar-sm me-2">
                                    <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                                </div>
                                <?= esc($user['first_name'] . ' ' . $user['last_name']) ?>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?= base_url('user/dashboard') ?>">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('user/profile') ?>">
                                <i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('user/settings') ?>">
                                <i class="fas fa-cog me-2"></i>Settings</a></li>
                            <?php if (in_array($user['role'], ['admin', 'editor'])): ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= base_url('admin/dashboard') ?>">
                                <i class="fas fa-tools me-2"></i>Admin Panel</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= base_url('auth/logout') ?>">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2">
                <div class="user-sidebar">
                    <div class="p-3">
                        <nav class="nav flex-column">
                            <a class="nav-link <?= (current_url() == base_url('user/dashboard')) ? 'active' : '' ?>" href="<?= base_url('user/dashboard') ?>">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                            <a class="nav-link <?= (current_url() == base_url('user/profile')) ? 'active' : '' ?>" href="<?= base_url('user/profile') ?>">
                                <i class="fas fa-user me-2"></i>Profile
                            </a>
                            
                            <?php if (in_array($user['role'], ['admin', 'editor', 'author'])): ?>
                            <a class="nav-link <?= (strpos(current_url(), 'user/posts') !== false) ? 'active' : '' ?>" href="<?= base_url('user/posts') ?>">
                                <i class="fas fa-blog me-2"></i>My Posts
                            </a>
                            <a class="nav-link <?= (strpos(current_url(), 'user/pages') !== false) ? 'active' : '' ?>" href="<?= base_url('user/pages') ?>">
                                <i class="fas fa-file-alt me-2"></i>My Pages
                            </a>
                            <a class="nav-link <?= (strpos(current_url(), 'user/media') !== false) ? 'active' : '' ?>" href="<?= base_url('user/media') ?>">
                                <i class="fas fa-images me-2"></i>My Media
                            </a>
                            <?php endif; ?>
                            
                            <a class="nav-link <?= (current_url() == base_url('user/settings')) ? 'active' : '' ?>" href="<?= base_url('user/settings') ?>">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                            
                            <hr class="my-3">
                            
                            <a class="nav-link" href="<?= base_url('auth/logout') ?>">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="user-main">
                    <!-- Flash Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show mb-4">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show mb-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show mb-4">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= $error ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Page Content -->
                    <?= $this->renderSection('content') ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- User Dashboard JavaScript -->
    <style>
        .user-avatar-sm {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
    
    <script>
        // Auto-hide alerts
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    if (alert.classList.contains('show')) {
                        alert.classList.remove('show');
                        alert.classList.add('fade');
                    }
                });
            }, 5000);
        });
    </script>
    
    <?= $this->renderSection('scripts') ?>
</body>
</html>
