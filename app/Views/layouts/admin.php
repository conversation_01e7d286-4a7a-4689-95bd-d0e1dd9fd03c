<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->renderSection('title', true) ?> - CMS Admin</title>
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- Custom Admin Styles -->
    <style>
        :root {
            --admin-primary: #4f46e5;
            --admin-primary-dark: #3730a3;
            --admin-secondary: #6b7280;
            --admin-success: #10b981;
            --admin-warning: #f59e0b;
            --admin-danger: #ef4444;
            --admin-info: #3b82f6;
            --admin-light: #f8fafc;
            --admin-dark: #1e293b;
            --admin-sidebar-width: 280px;
            --admin-sidebar-collapsed: 70px;
            --admin-header-height: 70px;
            --admin-border-color: #e2e8f0;
            --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--admin-light);
            margin: 0;
            padding: 0;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Sidebar Styles */
        .admin-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--admin-sidebar-width);
            height: 100vh;
            background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-dark) 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .admin-sidebar.collapsed {
            width: var(--admin-sidebar-collapsed);
        }

        .admin-sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .admin-sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .admin-sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-brand h4 {
            margin: 0;
            font-weight: 700;
            font-size: 18px;
            white-space: nowrap;
            transition: opacity 0.3s ease;
        }

        .admin-sidebar.collapsed .sidebar-brand h4 {
            opacity: 0;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 30px;
        }

        .nav-section-title {
            padding: 0 20px 10px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: rgba(255, 255, 255, 0.6);
            transition: opacity 0.3s ease;
        }

        .admin-sidebar.collapsed .nav-section-title {
            opacity: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-left-color: rgba(255, 255, 255, 0.3);
        }

        .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-left-color: white;
        }

        .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 12px;
            font-size: 16px;
        }

        .nav-link-text {
            white-space: nowrap;
            transition: opacity 0.3s ease;
        }

        .admin-sidebar.collapsed .nav-link-text {
            opacity: 0;
        }

        .nav-badge {
            margin-left: auto;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            transition: opacity 0.3s ease;
        }

        .admin-sidebar.collapsed .nav-badge {
            opacity: 0;
        }

        /* Main Content Area */
        .admin-main {
            margin-left: var(--admin-sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }

        .admin-main.expanded {
            margin-left: var(--admin-sidebar-collapsed);
        }

        /* Header Styles */
        .admin-header {
            background: white;
            border-bottom: 1px solid var(--admin-border-color);
            padding: 0 30px;
            height: var(--admin-header-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--admin-shadow);
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--admin-secondary);
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .sidebar-toggle:hover {
            background: var(--admin-light);
            color: var(--admin-primary);
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--admin-secondary);
            font-size: 14px;
        }

        .breadcrumb-nav a {
            color: var(--admin-primary);
            text-decoration: none;
        }

        .breadcrumb-nav a:hover {
            text-decoration: underline;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-search {
            position: relative;
        }

        .header-search input {
            border: 1px solid var(--admin-border-color);
            border-radius: 8px;
            padding: 8px 12px 8px 40px;
            width: 300px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .header-search input:focus {
            outline: none;
            border-color: var(--admin-primary);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .header-search i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--admin-secondary);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-btn {
            background: none;
            border: none;
            color: var(--admin-secondary);
            font-size: 18px;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        .header-btn:hover {
            background: var(--admin-light);
            color: var(--admin-primary);
        }

        .header-btn .badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background: var(--admin-danger);
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 8px;
            min-width: 16px;
            text-align: center;
        }

        .user-dropdown .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            background: none;
            border: none;
            color: var(--admin-dark);
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-toggle:hover {
            background: var(--admin-light);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-info));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .user-info {
            text-align: left;
        }

        .user-name {
            font-weight: 600;
            font-size: 14px;
            line-height: 1.2;
        }

        .user-role {
            font-size: 12px;
            color: var(--admin-secondary);
            line-height: 1.2;
        }

        /* Content Area */
        .admin-content {
            padding: 30px;
        }

        /* Page Header */
        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--admin-dark);
            margin: 0 0 8px 0;
        }

        .page-subtitle {
            color: var(--admin-secondary);
            margin: 0;
            font-size: 16px;
        }

        .page-actions {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 20px;
        }

        /* Responsive Design - Mobile First Approach */

        /* Large Desktop (1200px and up) */
        @media (min-width: 1200px) {
            .admin-sidebar {
                width: 280px;
            }

            .admin-main {
                margin-left: 280px;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Desktop (992px to 1199px) */
        @media (max-width: 1199px) {
            .admin-sidebar {
                width: 260px;
            }

            .admin-main {
                margin-left: 260px;
            }

            .stats-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 16px;
            }

            .page-actions {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }
        }

        /* Tablet (768px to 991px) */
        @media (max-width: 991px) {
            .admin-sidebar {
                width: 240px;
            }

            .admin-main {
                margin-left: 240px;
            }

            .admin-sidebar .nav-link {
                padding: 10px 16px;
                font-size: 14px;
            }

            .admin-sidebar .nav-link i {
                width: 18px;
                font-size: 14px;
            }

            .header-search input {
                max-width: 200px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .admin-content {
                padding: 20px;
            }

            .page-actions .btn {
                flex: 1;
                min-width: 120px;
            }
        }

        /* Mobile (up to 767px) */
        @media (max-width: 767px) {
            .admin-sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                width: 280px;
                z-index: 1050;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            }

            .admin-sidebar.mobile-open {
                transform: translateX(0);
            }

            .admin-main {
                margin-left: 0;
                padding: 16px;
            }

            .admin-header {
                padding: 12px 16px;
            }

            .admin-header .navbar-toggler {
                display: block !important;
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                padding: 8px;
                margin-right: 12px;
            }

            .header-search {
                display: none;
            }

            .admin-header .admin-user-dropdown .dropdown-toggle {
                padding: 8px 12px;
                font-size: 14px;
            }

            .admin-header .user-avatar {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            .page-header {
                margin-bottom: 20px;
                flex-direction: column;
                align-items: flex-start !important;
                gap: 12px;
            }

            .page-title {
                font-size: 24px;
                margin-bottom: 8px;
                line-height: 1.3;
            }

            .page-subtitle {
                font-size: 14px;
                margin-bottom: 0;
            }

            .page-actions {
                width: 100%;
                flex-direction: column;
                gap: 8px;
            }

            .page-actions .btn {
                width: 100%;
                justify-content: center;
            }

            .admin-breadcrumb {
                font-size: 13px;
                margin-bottom: 16px;
                flex-wrap: wrap;
                gap: 4px;
            }

            .admin-breadcrumb i {
                font-size: 10px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-value {
                font-size: 28px;
            }

            .stat-icon {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }

            .admin-table {
                font-size: 14px;
                overflow-x: auto;
            }

            .admin-table th,
            .admin-table td {
                padding: 12px 8px;
                min-width: 100px;
            }

            .admin-card-body {
                padding: 20px;
            }

            .admin-form-control {
                padding: 12px 16px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .btn {
                padding: 12px 20px;
                font-size: 14px;
                min-height: 44px; /* Touch-friendly */
            }

            .btn-sm {
                padding: 8px 16px;
                font-size: 13px;
                min-height: 36px;
            }

            /* Mobile sidebar overlay */
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1040;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .sidebar-overlay.show {
                opacity: 1;
                visibility: visible;
            }
        }

        /* Small Mobile (up to 575px) */
        @media (max-width: 575px) {
            .admin-main {
                padding: 12px;
            }

            .admin-header {
                padding: 10px 12px;
            }

            .page-header {
                margin-bottom: 16px;
            }

            .page-title {
                font-size: 20px;
            }

            .page-subtitle {
                font-size: 13px;
            }

            .admin-breadcrumb {
                font-size: 12px;
            }

            .stats-grid {
                gap: 12px;
            }

            .stat-card {
                padding: 16px;
                text-align: center;
            }

            .stat-value {
                font-size: 24px;
                margin-bottom: 4px;
            }

            .stat-label {
                font-size: 12px;
            }

            .stat-icon {
                position: relative;
                top: auto;
                right: auto;
                width: 36px;
                height: 36px;
                font-size: 16px;
                margin: 0 auto 8px;
            }

            .admin-table {
                font-size: 13px;
            }

            .admin-table th,
            .admin-table td {
                padding: 8px 6px;
                font-size: 12px;
            }

            .admin-card-body {
                padding: 16px;
            }

            .admin-form-control {
                padding: 10px 14px;
            }

            .btn {
                padding: 10px 16px;
                font-size: 13px;
            }

            .btn-sm {
                padding: 6px 12px;
                font-size: 12px;
            }

            /* Mobile-specific utilities */
            .d-mobile-none {
                display: none !important;
            }

            .d-mobile-block {
                display: block !important;
            }

            .text-mobile-center {
                text-align: center !important;
            }

            .flex-mobile-column {
                flex-direction: column !important;
            }
        }

        /* Touch device improvements */
        @media (hover: none) and (pointer: coarse) {
            .admin-sidebar .nav-link {
                padding: 14px 16px;
                min-height: 48px;
                display: flex;
                align-items: center;
            }

            .btn {
                min-height: 44px;
                padding: 12px 20px;
            }

            .btn-sm {
                min-height: 36px;
                padding: 8px 16px;
            }

            .admin-form-control {
                min-height: 44px;
                padding: 12px 16px;
            }

            .admin-table th,
            .admin-table td {
                padding: 12px 16px;
            }

            .dropdown-item {
                padding: 12px 16px;
                min-height: 44px;
                display: flex;
                align-items: center;
            }
        }

        /* Print styles */
        @media print {
            .admin-sidebar,
            .admin-header,
            .page-actions,
            .btn {
                display: none !important;
            }

            .admin-main {
                margin-left: 0 !important;
                padding: 0 !important;
            }

            .admin-card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }

            .page-title {
                color: #000 !important;
            }
        }

        /* Admin Components */
        .admin-card {
            background: white;
            border-radius: 12px;
            box-shadow: var(--admin-shadow);
            border: 1px solid var(--admin-border-color);
            overflow: hidden;
        }

        .admin-card-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--admin-border-color);
            background: #fafbfc;
        }

        .admin-card-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--admin-dark);
        }

        .admin-card-subtitle {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: var(--admin-secondary);
        }

        .admin-card-body {
            padding: 24px;
        }

        .admin-card-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--admin-border-color);
            background: #fafbfc;
        }

        /* Buttons */
        .btn-admin-primary {
            background: var(--admin-primary);
            border-color: var(--admin-primary);
            color: white;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .btn-admin-primary:hover {
            background: var(--admin-primary-dark);
            border-color: var(--admin-primary-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
        }

        .btn-admin-secondary {
            background: white;
            border: 1px solid var(--admin-border-color);
            color: var(--admin-secondary);
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .btn-admin-secondary:hover {
            background: var(--admin-light);
            border-color: var(--admin-primary);
            color: var(--admin-primary);
        }

        /* Tables */
        .admin-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--admin-shadow);
        }

        .admin-table table {
            margin: 0;
        }

        .admin-table th {
            background: #fafbfc;
            border-bottom: 1px solid var(--admin-border-color);
            font-weight: 600;
            color: var(--admin-dark);
            padding: 16px;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .admin-table td {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .admin-table tbody tr:hover {
            background: #fafbfc;
        }

        /* Forms */
        .admin-form-group {
            margin-bottom: 24px;
        }

        .admin-form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--admin-dark);
            font-size: 14px;
        }

        .admin-form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--admin-border-color);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: white;
        }

        .admin-form-control:focus {
            outline: none;
            border-color: var(--admin-primary);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .admin-form-help {
            margin-top: 6px;
            font-size: 12px;
            color: var(--admin-secondary);
        }

        /* Status Badges */
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--admin-success);
        }

        .status-badge.inactive {
            background: rgba(107, 114, 128, 0.1);
            color: var(--admin-secondary);
        }

        .status-badge.published {
            background: rgba(16, 185, 129, 0.1);
            color: var(--admin-success);
        }

        .status-badge.draft {
            background: rgba(245, 158, 11, 0.1);
            color: var(--admin-warning);
        }

        .status-badge.private {
            background: rgba(239, 68, 68, 0.1);
            color: var(--admin-danger);
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: var(--admin-shadow);
            border: 1px solid var(--admin-border-color);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--admin-primary);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--admin-dark);
            margin: 0 0 8px 0;
        }

        .stat-label {
            font-size: 14px;
            color: var(--admin-secondary);
            margin: 0 0 12px 0;
        }

        .stat-change {
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-change.positive {
            color: var(--admin-success);
        }

        .stat-change.negative {
            color: var(--admin-danger);
        }

        .stat-icon {
            position: absolute;
            top: 24px;
            right: 24px;
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            background: var(--admin-primary);
        }

        /* Loading States */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid var(--admin-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Utilities */
        .text-admin-primary { color: var(--admin-primary) !important; }
        .text-admin-secondary { color: var(--admin-secondary) !important; }
        .text-admin-success { color: var(--admin-success) !important; }
        .text-admin-warning { color: var(--admin-warning) !important; }
        .text-admin-danger { color: var(--admin-danger) !important; }
        .text-admin-info { color: var(--admin-info) !important; }

        .bg-admin-primary { background-color: var(--admin-primary) !important; }
        .bg-admin-light { background-color: var(--admin-light) !important; }

        .border-admin { border-color: var(--admin-border-color) !important; }

        .shadow-admin { box-shadow: var(--admin-shadow) !important; }
        .shadow-admin-lg { box-shadow: var(--admin-shadow-lg) !important; }

        /* Additional Components */
        .admin-alert {
            border: none;
            border-radius: 8px;
            padding: 16px 20px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .admin-alert.alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-left-color: var(--admin-success);
            color: var(--admin-success);
        }

        .admin-alert.alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border-left-color: var(--admin-danger);
            color: var(--admin-danger);
        }

        .admin-alert.alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-left-color: var(--admin-warning);
            color: var(--admin-warning);
        }

        .admin-alert.alert-info {
            background: rgba(59, 130, 246, 0.1);
            border-left-color: var(--admin-info);
            color: var(--admin-info);
        }

        /* Dropdown Improvements */
        .dropdown-menu {
            border: 1px solid var(--admin-border-color);
            border-radius: 8px;
            box-shadow: var(--admin-shadow-lg);
            padding: 8px 0;
        }

        .dropdown-item {
            padding: 10px 16px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background: var(--admin-light);
            color: var(--admin-primary);
        }

        .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        /* Modal Improvements */
        .modal-content {
            border: none;
            border-radius: 12px;
            box-shadow: var(--admin-shadow-lg);
        }

        .modal-header {
            border-bottom: 1px solid var(--admin-border-color);
            padding: 20px 24px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            border-top: 1px solid var(--admin-border-color);
            padding: 16px 24px;
        }

        /* Pagination */
        .pagination {
            margin: 0;
        }

        .page-link {
            border: 1px solid var(--admin-border-color);
            color: var(--admin-secondary);
            padding: 8px 12px;
            margin: 0 2px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .page-link:hover {
            background: var(--admin-light);
            border-color: var(--admin-primary);
            color: var(--admin-primary);
        }

        .page-item.active .page-link {
            background: var(--admin-primary);
            border-color: var(--admin-primary);
            color: white;
        }

        /* Progress Bars */
        .progress {
            height: 8px;
            border-radius: 4px;
            background: var(--admin-light);
        }

        .progress-bar {
            border-radius: 4px;
        }

        /* Tooltips */
        .tooltip {
            font-size: 12px;
        }

        .tooltip-inner {
            background: var(--admin-dark);
            border-radius: 6px;
            padding: 6px 10px;
        }

        /* File Upload Areas */
        .admin-upload-area {
            border: 2px dashed var(--admin-border-color);
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .admin-upload-area:hover {
            border-color: var(--admin-primary);
            background: rgba(79, 70, 229, 0.05);
        }

        .admin-upload-area.dragover {
            border-color: var(--admin-primary);
            background: rgba(79, 70, 229, 0.1);
        }

        /* Code Blocks */
        .admin-code {
            background: #f8f9fa;
            border: 1px solid var(--admin-border-color);
            border-radius: 6px;
            padding: 12px 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            color: var(--admin-dark);
        }

        /* Empty States */
        .admin-empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--admin-secondary);
        }

        .admin-empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .admin-empty-state h4 {
            color: var(--admin-dark);
            margin-bottom: 12px;
        }

        .admin-empty-state p {
            margin-bottom: 24px;
        }

        /* Timeline */
        .admin-timeline {
            position: relative;
            padding-left: 30px;
        }

        .admin-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--admin-border-color);
        }

        .admin-timeline-item {
            position: relative;
            margin-bottom: 30px;
        }

        .admin-timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--admin-primary);
            border: 3px solid white;
            box-shadow: 0 0 0 2px var(--admin-border-color);
        }

        /* Responsive Improvements */
        @media (max-width: 992px) {
            .page-actions {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: stretch;
                gap: 20px;
            }

            .admin-card-header {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }

            .btn-group {
                flex-wrap: wrap;
            }
        }
    </style>
    
    <?= $this->renderSection('head') ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-brand">
            <i class="fas fa-cogs"></i>
            <h4>CMS Admin</h4>
        </div>
        
        <nav class="sidebar-nav">
            <!-- Dashboard -->
            <div class="nav-section">
                <a href="<?= base_url('admin/dashboard') ?>" class="nav-link <?= (current_url() == base_url('admin/dashboard') || current_url() == base_url('admin')) ? 'active' : '' ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="nav-link-text">Dashboard</span>
                </a>
            </div>
            
            <!-- Content Management -->
            <div class="nav-section">
                <div class="nav-section-title">Content</div>
                
                <?php if (in_array($user['role'], ['admin', 'editor'])): ?>
                <a href="<?= base_url('admin/pages') ?>" class="nav-link <?= (strpos(current_url(), 'admin/pages') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-file-alt"></i>
                    <span class="nav-link-text">Pages</span>
                </a>
                <?php endif; ?>
                
                <a href="<?= base_url('admin/blog') ?>" class="nav-link <?= (strpos(current_url(), 'admin/blog') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-blog"></i>
                    <span class="nav-link-text">Blog Posts</span>
                    <?php $nav_stats = get_admin_stats(); ?>
                    <span class="nav-badge"><?= $nav_stats['total_posts'] ?></span>
                </a>
                
                <a href="<?= base_url('admin/media') ?>" class="nav-link <?= (strpos(current_url(), 'admin/media') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-images"></i>
                    <span class="nav-link-text">Media Library</span>
                </a>
                
                <?php if (in_array($user['role'], ['admin', 'editor'])): ?>
                <a href="<?= base_url('admin/categories') ?>" class="nav-link <?= (strpos(current_url(), 'admin/categories') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-folder"></i>
                    <span class="nav-link-text">Categories</span>
                </a>
                
                <a href="<?= base_url('admin/tags') ?>" class="nav-link <?= (strpos(current_url(), 'admin/tags') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-tags"></i>
                    <span class="nav-link-text">Tags</span>
                </a>
                
                <a href="<?= base_url('admin/menus') ?>" class="nav-link <?= (strpos(current_url(), 'admin/menus') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-bars"></i>
                    <span class="nav-link-text">Menus</span>
                </a>
                <?php endif; ?>
            </div>
            
            <!-- Design & Templates -->
            <div class="nav-section">
                <div class="nav-section-title">Design</div>
                
                <a href="<?= base_url('admin/themes') ?>" class="nav-link <?= (strpos(current_url(), 'admin/themes') !== false && strpos(current_url(), 'templates') === false) ? 'active' : '' ?>">
                    <i class="fas fa-paint-brush"></i>
                    <span class="nav-link-text">Themes</span>
                </a>
                
                <a href="<?= base_url('admin/templates/marketplace') ?>" class="nav-link <?= (strpos(current_url(), 'templates/marketplace') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-store"></i>
                    <span class="nav-link-text">Template Store</span>
                </a>
                
                <a href="<?= base_url('admin/templates/bulk-manager') ?>" class="nav-link <?= (strpos(current_url(), 'templates/bulk-manager') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-link-text">Bulk Manager</span>
                </a>
                
                <a href="<?= base_url('admin/templates/analytics') ?>" class="nav-link <?= (strpos(current_url(), 'templates/analytics') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-chart-line"></i>
                    <span class="nav-link-text">Template Analytics</span>
                </a>
            </div>
            
            <!-- System -->
            <?php if ($user['role'] === 'admin'): ?>
            <div class="nav-section">
                <div class="nav-section-title">System</div>
                
                <a href="<?= base_url('admin/users') ?>" class="nav-link <?= (strpos(current_url(), 'admin/users') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-users"></i>
                    <span class="nav-link-text">Users</span>
                    <span class="nav-badge"><?= $nav_stats['total_users'] ?></span>
                </a>
                
                <a href="<?= base_url('admin/settings') ?>" class="nav-link <?= (strpos(current_url(), 'admin/settings') !== false) ? 'active' : '' ?>">
                    <i class="fas fa-cog"></i>
                    <span class="nav-link-text">Settings</span>
                </a>
            </div>
            <?php endif; ?>
            
            <!-- Quick Actions -->
            <div class="nav-section">
                <div class="nav-section-title">Quick Actions</div>
                
                <a href="<?= base_url() ?>" target="_blank" class="nav-link">
                    <i class="fas fa-external-link-alt"></i>
                    <span class="nav-link-text">View Site</span>
                </a>
                
                <a href="<?= base_url('user/profile') ?>" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span class="nav-link-text">My Profile</span>
                </a>
                
                <a href="<?= base_url('auth/logout') ?>" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="nav-link-text">Logout</span>
                </a>
            </div>
        </nav>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeMobileSidebar()"></div>

    <!-- Main Content -->
    <div class="admin-main" id="adminMain">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <nav class="breadcrumb-nav">
                    <?= $this->renderSection('breadcrumb') ?>
                </nav>
            </div>
            
            <div class="header-right">
                <!-- Global Search -->
                <div class="header-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search..." id="globalSearch">
                </div>
                
                <!-- Header Actions -->
                <div class="header-actions">
                    <button class="header-btn" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </button>

                    <div class="dropdown">
                        <button class="header-btn dropdown-toggle" type="button" data-bs-toggle="dropdown" title="Quick Add">
                            <i class="fas fa-plus"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <?php if (in_array($user['role'], ['admin', 'editor', 'author'])): ?>
                            <li><a class="dropdown-item" href="<?= base_url('admin/blog/create') ?>">
                                <i class="fas fa-blog me-2"></i>New Blog Post</a></li>
                            <?php endif; ?>

                            <?php if (in_array($user['role'], ['admin', 'editor'])): ?>
                            <li><a class="dropdown-item" href="<?= base_url('admin/pages/create') ?>">
                                <i class="fas fa-file-alt me-2"></i>New Page</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('admin/categories/create') ?>">
                                <i class="fas fa-folder me-2"></i>New Category</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('admin/tags/create') ?>">
                                <i class="fas fa-tag me-2"></i>New Tag</a></li>
                            <?php endif; ?>

                            <li><a class="dropdown-item" href="<?= base_url('admin/media') ?>">
                                <i class="fas fa-upload me-2"></i>Upload Media</a></li>

                            <?php if ($user['role'] === 'admin'): ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= base_url('admin/users/create') ?>">
                                <i class="fas fa-user-plus me-2"></i>New User</a></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
                
                <!-- User Dropdown -->
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <div class="user-avatar">
                            <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                        </div>
                        <div class="user-info">
                            <div class="user-name"><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></div>
                            <div class="user-role"><?= ucfirst($user['role']) ?></div>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?= base_url('user/dashboard') ?>">
                            <i class="fas fa-tachometer-alt me-2"></i>My Dashboard</a></li>
                        <li><a class="dropdown-item" href="<?= base_url('user/profile') ?>">
                            <i class="fas fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="<?= base_url('user/settings') ?>">
                            <i class="fas fa-cog me-2"></i>Account Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?= base_url() ?>" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Website</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?= base_url('auth/logout') ?>">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </header>
        
        <!-- Content Area -->
        <main class="admin-content">
            <!-- Flash Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show mb-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show mb-4">
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= $error ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Page Content -->
            <?= $this->renderSection('content') ?>
        </main>
    </div>
    
    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <!-- Admin JavaScript -->
    <script>
        // Sidebar Toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('adminSidebar');
            const main = document.getElementById('adminMain');
            const overlay = document.getElementById('sidebarOverlay');

            // Check if mobile view
            if (window.innerWidth <= 767) {
                sidebar.classList.toggle('mobile-open');
                if (overlay) {
                    overlay.classList.toggle('show');
                }
            } else {
                sidebar.classList.toggle('collapsed');
                main.classList.toggle('expanded');

                // Save state for desktop
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            }
        }

        // Close mobile sidebar
        function closeMobileSidebar() {
            const sidebar = document.getElementById('adminSidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.remove('mobile-open');
            if (overlay) {
                overlay.classList.remove('show');
            }
        }

        // Restore sidebar state
        document.addEventListener('DOMContentLoaded', function() {
            // Only restore collapsed state on desktop
            if (window.innerWidth > 767) {
                const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
                if (isCollapsed) {
                    document.getElementById('adminSidebar').classList.add('collapsed');
                    document.getElementById('adminMain').classList.add('expanded');
                }
            }

            // Auto-hide alerts
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);

            // Initialize Select2
            $('.select2').select2({
                theme: 'bootstrap-5'
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                const sidebar = document.getElementById('adminSidebar');
                const overlay = document.getElementById('sidebarOverlay');

                if (window.innerWidth > 767) {
                    // Desktop view - remove mobile classes
                    sidebar.classList.remove('mobile-open');
                    if (overlay) {
                        overlay.classList.remove('show');
                    }
                } else {
                    // Mobile view - remove desktop classes
                    sidebar.classList.remove('collapsed');
                    document.getElementById('adminMain').classList.remove('expanded');
                }
            });
        });
        
        // Global search functionality
        let searchTimeout;
        document.getElementById('globalSearch').addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();

            if (query.length < 2) return;

            searchTimeout = setTimeout(() => {
                performGlobalSearch(query);
            }, 300);
        });

        function performGlobalSearch(query) {
            // Show search results dropdown
            showSearchResults(query);
        }

        function showSearchResults(query) {
            // This would typically make an AJAX call to search across content
            const mockResults = [
                { type: 'post', title: 'Sample Blog Post', url: '/admin/blog/edit/1' },
                { type: 'page', title: 'About Page', url: '/admin/pages/edit/1' },
                { type: 'user', title: 'John Doe', url: '/admin/users/profile/1' }
            ].filter(item => item.title.toLowerCase().includes(query.toLowerCase()));

            // Create and show results dropdown (simplified implementation)
            console.log('Search results for:', query, mockResults);
        }
        
        // Global alert function
        function showAlert(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
            const icon = type === 'error' ? 'fas fa-exclamation-triangle' : 
                        type === 'success' ? 'fas fa-check-circle' : 'fas fa-info-circle';
            
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show mb-4">
                    <i class="${icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.admin-content').prepend(alertHtml);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $('.alert').first().fadeOut();
            }, 5000);
        }
        
        // Enhanced confirmation dialog helper
        function confirmAction(message, callback, options = {}) {
            const defaultOptions = {
                title: 'Confirm Action',
                confirmText: 'Confirm',
                cancelText: 'Cancel',
                type: 'warning' // warning, danger, info
            };

            const config = { ...defaultOptions, ...options };

            // Create custom modal for better UX (simplified version)
            const confirmed = confirm(`${config.title}\n\n${message}`);
            if (confirmed) {
                callback();
            }
        }

        // Specific confirmation dialogs
        function confirmDelete(itemName, callback) {
            confirmAction(
                `Are you sure you want to delete "${itemName}"? This action cannot be undone.`,
                callback,
                {
                    title: 'Delete Confirmation',
                    confirmText: 'Delete',
                    type: 'danger'
                }
            );
        }

        function confirmBulkAction(action, count, callback) {
            const actionText = action.charAt(0).toUpperCase() + action.slice(1);
            confirmAction(
                `Are you sure you want to ${action} ${count} item(s)?`,
                callback,
                {
                    title: `${actionText} Confirmation`,
                    confirmText: actionText,
                    type: action === 'delete' ? 'danger' : 'warning'
                }
            );
        }
        
        // Loading state helper
        function setLoading(element, loading = true) {
            if (loading) {
                element.disabled = true;
                const originalText = element.innerHTML;
                element.setAttribute('data-original-text', originalText);
                element.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
            } else {
                element.disabled = false;
                element.innerHTML = element.getAttribute('data-original-text') || 'Submit';
            }
        }

        // Admin API helper
        function adminAPI(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };

            return fetch(url, { ...defaultOptions, ...options })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('API Error:', error);
                    showAlert('An error occurred. Please try again.', 'error');
                    throw error;
                });
        }

        // Bulk action helper
        function handleBulkAction(action, selectedIds, confirmMessage, apiUrl) {
            if (selectedIds.length === 0) {
                showAlert('Please select at least one item', 'warning');
                return;
            }

            if (confirm(confirmMessage)) {
                adminAPI(apiUrl, {
                    method: 'POST',
                    body: JSON.stringify({
                        action: action,
                        ids: selectedIds
                    })
                })
                .then(data => {
                    if (data.success) {
                        showAlert(data.message || 'Action completed successfully', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert(data.message || 'Action failed', 'error');
                    }
                });
            }
        }

        // Form validation helper
        function validateForm(formElement) {
            const inputs = formElement.querySelectorAll('[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // File upload helper
        function initFileUpload(element, options = {}) {
            const defaultOptions = {
                maxSize: 10 * 1024 * 1024, // 10MB
                allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
                multiple: false
            };

            const config = { ...defaultOptions, ...options };

            element.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);

                files.forEach(file => {
                    if (file.size > config.maxSize) {
                        showAlert(`File ${file.name} is too large. Maximum size is ${formatFileSize(config.maxSize)}.`, 'error');
                        return;
                    }

                    if (!config.allowedTypes.includes(file.type)) {
                        showAlert(`File ${file.name} has an invalid type.`, 'error');
                        return;
                    }
                });
            });
        }

        // Format file size
        function formatFileSize(bytes) {
            const units = ['B', 'KB', 'MB', 'GB'];
            let size = bytes;
            let unitIndex = 0;

            while (size >= 1024 && unitIndex < units.length - 1) {
                size /= 1024;
                unitIndex++;
            }

            return `${size.toFixed(1)} ${units[unitIndex]}`;
        }

        // Initialize tooltips
        function initTooltips() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initTooltips();

            // Initialize file uploads
            document.querySelectorAll('input[type="file"]').forEach(input => {
                initFileUpload(input);
            });

            // Auto-focus first input in modals
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('shown.bs.modal', function() {
                    const firstInput = modal.querySelector('input, textarea, select');
                    if (firstInput) firstInput.focus();
                });
            });
        });
    </script>
    
    <?= $this->renderSection('scripts') ?>
</body>
</html>
