<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>User Management<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<span>Users</span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
    <style>
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
    </style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">User Management</h1>
        <p class="page-subtitle">Manage user accounts, roles, and permissions</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/users/create') ?>" class="btn btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New User
        </a>
    </div>
</div>
<!-- Users Table -->
<div class="admin-card">
    <div class="admin-card-header d-flex justify-content-between align-items-center">
        <div>
            <h3 class="admin-card-title">All Users</h3>
            <p class="admin-card-subtitle">Manage user accounts and permissions</p>
        </div>
        <div class="d-flex align-items-center gap-3">
            <div class="input-group" style="width: 300px;">
                <input type="text" class="form-control" id="searchUsers" placeholder="Search users...">
                <button class="btn btn-outline-secondary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="admin-card-body p-0">
        <?php if (!empty($users)): ?>
        <div class="admin-table">
            <table class="table table-hover mb-0" id="usersTable">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th>User</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Last Login</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                                        <?php foreach ($users as $u): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input user-checkbox" value="<?= $u['id'] ?>">
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="user-avatar bg-primary text-white d-flex align-items-center justify-content-center me-3">
                                                        <?= strtoupper(substr($u['first_name'], 0, 1) . substr($u['last_name'], 0, 1)) ?>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold"><?= esc($u['first_name'] . ' ' . $u['last_name']) ?></div>
                                                        <small class="text-muted">@<?= esc($u['username']) ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?= esc($u['email']) ?>
                                                <?php if (!empty($u['email_verified_at'])): ?>
                                                    <i class="fas fa-check-circle text-success ms-1" title="Verified on <?= date('M j, Y', strtotime($u['email_verified_at'])) ?>"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-exclamation-circle text-warning ms-1" title="Email not verified"></i>
                                                <?php endif; ?>
                                            </td>
                        <td>
                            <span class="badge bg-<?= function_exists('admin_role_color') ? admin_role_color($u['role']) : 'secondary' ?>">
                                <?= ucfirst($u['role']) ?>
                            </span>
                        </td>
                        <td>
                            <?php if (function_exists('admin_status_badge')): ?>
                                <?= admin_status_badge($u['status']) ?>
                            <?php else: ?>
                                <span class="badge bg-<?= $u['status'] === 'active' ? 'success' : 'secondary' ?>">
                                    <?= ucfirst($u['status']) ?>
                                </span>
                            <?php endif; ?>
                        </td>
                                            <td>
                                                <?php if ($u['last_login']): ?>
                                                    <small><?= date('M j, Y g:i A', strtotime($u['last_login'])) ?></small>
                                                <?php else: ?>
                                                    <small class="text-muted">Never</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?= date('M j, Y', strtotime($u['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="<?= base_url('admin/users/profile/' . $u['id']) ?>"
                                                       class="btn btn-outline-info" title="View Profile">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/users/edit/' . $u['id']) ?>"
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($u['id'] != $user['id']): ?>
                                                    <!-- Email Verification Toggle -->
                                                    <?php if (!empty($u['email_verified_at'])): ?>
                                                    <button type="button" class="btn btn-outline-warning"
                                                            onclick="unverifyEmail(<?= $u['id'] ?>)"
                                                            title="Remove Email Verification">
                                                        <i class="fas fa-envelope-open-text"></i>
                                                    </button>
                                                    <?php else: ?>
                                                    <button type="button" class="btn btn-outline-success"
                                                            onclick="verifyEmail(<?= $u['id'] ?>)"
                                                            title="Verify Email">
                                                        <i class="fas fa-envelope-circle-check"></i>
                                                    </button>
                                                    <?php endif; ?>

                                                    <!-- Status Toggle -->
                                                    <button type="button" class="btn btn-outline-<?= $u['status'] === 'active' ? 'warning' : 'success' ?>"
                                                            onclick="toggleStatus(<?= $u['id'] ?>)"
                                                            title="<?= $u['status'] === 'active' ? 'Deactivate' : 'Activate' ?>">
                                                        <i class="fas fa-<?= $u['status'] === 'active' ? 'pause' : 'play' ?>"></i>
                                                    </button>

                                    <!-- Delete -->
                                    <button type="button" class="btn btn-outline-danger"
                                            onclick="deleteUser(<?= $u['id'] ?>, '<?= esc($u['first_name'] . ' ' . $u['last_name']) ?>')" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Bulk Actions -->
            <div class="admin-card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center gap-3">
                        <select class="form-select form-select-sm" id="bulkAction" style="width: auto;">
                            <option value="">Bulk Actions</option>
                            <option value="activate">Activate</option>
                            <option value="deactivate">Deactivate</option>
                            <option value="delete">Delete</option>
                        </select>
                        <button type="button" class="btn btn-sm btn-admin-secondary" onclick="executeBulkAction()">
                            Apply
                        </button>
                    </div>
                    <small class="text-admin-secondary">
                        Showing <?= count($users) ?> users
                    </small>
                </div>
            </div>
            <?php else: ?>
            <div class="admin-empty-state">
                <i class="fas fa-users"></i>
                <h4>No Users Found</h4>
                <p>Start by creating your first user account.</p>
                <a href="<?= base_url('admin/users/create') ?>" class="btn btn-admin-primary">
                    <i class="fas fa-plus me-2"></i>Add New User
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#usersTable').DataTable({
                "pageLength": 25,
                "order": [[ 6, "desc" ]],
                "columnDefs": [
                    { "orderable": false, "targets": [0, 7] }
                ]
            });
            
            // Select all checkbox
            $('#selectAll').change(function() {
                $('.user-checkbox').prop('checked', this.checked);
            });
            
            // Individual checkbox change
            $('.user-checkbox').change(function() {
                if (!this.checked) {
                    $('#selectAll').prop('checked', false);
                }
            });
        });
        
        function toggleStatus(userId) {
            confirmAction('Are you sure you want to change this user\'s status?', function() {
                adminAPI(`<?= base_url('admin/users/toggle-status/') ?>${userId}`, {
                    method: 'POST'
                })
                .then(data => {
                    if (data.success) {
                        showAlert(data.message || 'User status updated successfully', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert(data.message || 'Failed to update user status', 'error');
                    }
                });
            });
        }
        
        function deleteUser(userId, userName = 'this user') {
            confirmDelete(userName, function() {
                adminAPI(`<?= base_url('admin/users/delete/') ?>${userId}`, {
                    method: 'DELETE'
                })
                .then(data => {
                    if (data.success) {
                        showAlert(data.message || 'User deleted successfully', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert(data.message || 'Failed to delete user', 'error');
                    }
                });
            });
        }
        
        function verifyEmail(userId) {
            confirmAction('Are you sure you want to verify this user\'s email?', function() {
                adminAPI(`<?= base_url('admin/users/verify-email/') ?>${userId}`, {
                    method: 'POST'
                })
                .then(data => {
                    if (data.success) {
                        showAlert(data.message || 'Email verified successfully', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert(data.message || 'Failed to verify email', 'error');
                    }
                });
            });
        }

        function unverifyEmail(userId) {
            confirmAction('Are you sure you want to remove email verification for this user?', function() {
                adminAPI(`<?= base_url('admin/users/unverify-email/') ?>${userId}`, {
                    method: 'POST'
                })
                .then(data => {
                    if (data.success) {
                        showAlert(data.message || 'Email verification removed successfully', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert(data.message || 'Failed to remove email verification', 'error');
                    }
                });
            });
        }

        function executeBulkAction() {
            const action = document.getElementById('bulkAction').value;
            const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);

            if (!action) {
                showAlert('Please select an action', 'warning');
                return;
            }

            if (selectedUsers.length === 0) {
                showAlert('Please select at least one user', 'warning');
                return;
            }

            confirmBulkAction(action, selectedUsers.length, function() {
                adminAPI('<?= base_url('admin/users/bulk-action') ?>', {
                    method: 'POST',
                    body: JSON.stringify({
                        action: action,
                        user_ids: selectedUsers
                    })
                })
                .then(data => {
                    if (data.success) {
                        showAlert(data.message || `${selectedUsers.length} user(s) ${action}ed successfully`, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert(data.message || 'Failed to execute bulk action', 'error');
                    }
                });
            });
        }
    </script>
</body>
</html>
