<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/users') ?>">Users</a>
<i class="fas fa-chevron-right"></i>
<span><?= $title ?></span>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">
            <i class="fas fa-user-edit me-2"></i><?= $title ?>
        </h1>
        <p class="page-subtitle">Update user account information</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/users') ?>" class="btn btn-admin-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Users
        </a>
    </div>
</div>

<!-- Edit User Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>User Information</h5>
            </div>
            <div class="admin-card-body">
                <form action="<?= base_url('admin/users/update/' . $edit_user['id']) ?>" method="post" id="editUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="first_name" class="admin-form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="admin-form-control" id="first_name" name="first_name"
                                       value="<?= old('first_name', $edit_user['first_name']) ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="last_name" class="admin-form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="admin-form-control" id="last_name" name="last_name"
                                       value="<?= old('last_name', $edit_user['last_name']) ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="username" class="admin-form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="admin-form-control" id="username" name="username"
                                       value="<?= old('username', $edit_user['username']) ?>" required>
                                <div class="admin-form-text">Must be unique and at least 3 characters long</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="email" class="admin-form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="admin-form-control" id="email" name="email"
                                       value="<?= old('email', $edit_user['email']) ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="password" class="admin-form-label">New Password</label>
                                <input type="password" class="admin-form-control" id="password" name="password">
                                <div class="admin-form-text">Leave blank to keep current password</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="role" class="admin-form-label">Role <span class="text-danger">*</span></label>
                                <select class="admin-form-control" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="admin" <?= old('role', $edit_user['role']) === 'admin' ? 'selected' : '' ?>>Administrator</option>
                                    <option value="editor" <?= old('role', $edit_user['role']) === 'editor' ? 'selected' : '' ?>>Editor</option>
                                    <option value="author" <?= old('role', $edit_user['role']) === 'author' ? 'selected' : '' ?>>Author</option>
                                    <option value="subscriber" <?= old('role', $edit_user['role']) === 'subscriber' ? 'selected' : '' ?>>Subscriber</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="admin-form-group">
                        <label for="status" class="admin-form-label">Status <span class="text-danger">*</span></label>
                        <select class="admin-form-control" id="status" name="status" required>
                            <option value="active" <?= old('status', $edit_user['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status', $edit_user['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('admin/users') ?>" class="btn btn-admin-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-admin-primary">
                            <i class="fas fa-save me-2"></i>Update User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>User Details</h6>
            </div>
            <div class="admin-card-body">
                <div class="mb-3">
                    <strong>User ID:</strong> <?= $edit_user['id'] ?>
                </div>
                <div class="mb-3">
                    <strong>Created:</strong> <?= date('M j, Y', strtotime($edit_user['created_at'])) ?>
                </div>
                <div class="mb-3">
                    <strong>Last Updated:</strong> <?= date('M j, Y', strtotime($edit_user['updated_at'])) ?>
                </div>
                <div class="mb-0">
                    <strong>Email Verified:</strong> 
                    <?php if ($edit_user['email_verified_at']): ?>
                        <span class="badge bg-success">Verified</span>
                    <?php else: ?>
                        <span class="badge bg-warning">Not Verified</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Security Notes</h6>
            </div>
            <div class="admin-card-body">
                <ul class="small mb-0">
                    <li>Leave password blank to keep current password</li>
                    <li>Email addresses must be unique</li>
                    <li>Usernames must be unique and at least 3 characters</li>
                    <li>Role changes take effect immediately</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Form validation
    document.getElementById('username').addEventListener('input', function() {
        this.value = this.value.toLowerCase().replace(/[^a-z0-9_]/g, '');
    });

    // Enhanced form submission
    document.getElementById('editUserForm').addEventListener('submit', function(e) {
        const requiredFields = ['first_name', 'last_name', 'username', 'email', 'role', 'status'];
        let isValid = true;

        requiredFields.forEach(field => {
            const input = document.getElementById(field);
            if (!input.value.trim()) {
                isValid = false;
                input.classList.add('is-invalid');
            } else {
                input.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            showAlert('Please fill in all required fields.', 'error');
            return false;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating User...';
        submitBtn.disabled = true;

        // Re-enable button after delay (in case of validation errors)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });
</script>
<?= $this->endSection() ?>
