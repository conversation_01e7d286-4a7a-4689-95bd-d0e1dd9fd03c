<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/users') ?>">Users</a>
<i class="fas fa-chevron-right"></i>
<span><?= $profile_user['first_name'] . ' ' . $profile_user['last_name'] ?></span>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">
            <i class="fas fa-user me-2"></i><?= esc($profile_user['first_name'] . ' ' . $profile_user['last_name']) ?>
        </h1>
        <p class="page-subtitle">User profile and activity overview</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/users/edit/' . $profile_user['id']) ?>" class="btn btn-admin-primary">
            <i class="fas fa-edit me-2"></i>Edit User
        </a>
        <a href="<?= base_url('admin/users') ?>" class="btn btn-admin-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Users
        </a>
    </div>
</div>

<!-- User Profile Content -->
<div class="row">
    <!-- User Information -->
    <div class="col-lg-4">
        <div class="admin-card mb-4">
            <div class="admin-card-body text-center">
                <div class="user-avatar mb-3">
                    <div class="avatar-circle bg-admin-primary text-white">
                        <i class="fas fa-user fa-3x"></i>
                    </div>
                </div>
                <h4 class="mb-1"><?= esc($profile_user['first_name'] . ' ' . $profile_user['last_name']) ?></h4>
                <p class="text-muted mb-2">@<?= esc($profile_user['username']) ?></p>
                <span class="badge bg-<?= admin_role_color($profile_user['role']) ?> mb-3">
                    <?= ucfirst($profile_user['role']) ?>
                </span>
                <div class="mb-3">
                    <?= admin_status_badge($profile_user['status']) ?>
                </div>
            </div>
        </div>

        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Account Details</h6>
            </div>
            <div class="admin-card-body">
                <div class="mb-3">
                    <strong>Email:</strong><br>
                    <span class="text-muted"><?= esc($profile_user['email']) ?></span>
                    <?php if ($profile_user['email_verified_at']): ?>
                        <i class="fas fa-check-circle text-success ms-1" title="Verified"></i>
                    <?php else: ?>
                        <i class="fas fa-exclamation-triangle text-warning ms-1" title="Not Verified"></i>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3">
                    <strong>User ID:</strong><br>
                    <span class="text-muted"><?= $profile_user['id'] ?></span>
                </div>
                
                <div class="mb-3">
                    <strong>Member Since:</strong><br>
                    <span class="text-muted"><?= date('F j, Y', strtotime($profile_user['created_at'])) ?></span>
                </div>
                
                <div class="mb-0">
                    <strong>Last Updated:</strong><br>
                    <span class="text-muted"><?= date('F j, Y g:i A', strtotime($profile_user['updated_at'])) ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- User Statistics and Activity -->
    <div class="col-lg-8">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-admin-primary">
                        <i class="fas fa-blog"></i>
                    </div>
                    <div class="stat-value"><?= $stats['total_posts'] ?? 0 ?></div>
                    <div class="stat-label">Blog Posts</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-value"><?= $stats['total_pages'] ?? 0 ?></div>
                    <div class="stat-label">Pages</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="stat-value"><?= $stats['total_media'] ?? 0 ?></div>
                    <div class="stat-label">Media Files</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="stat-value"><?= $stats['total_comments'] ?? 0 ?></div>
                    <div class="stat-label">Comments</div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="admin-card mb-4">
            <div class="admin-card-header">
                <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activity</h6>
            </div>
            <div class="admin-card-body">
                <?php if (!empty($stats['recent_posts'])): ?>
                    <h6 class="mb-3">Recent Blog Posts</h6>
                    <?php foreach ($stats['recent_posts'] as $post): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <div class="bg-admin-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-blog"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">
                                    <a href="<?= base_url('blog/' . $post['slug']) ?>" class="text-decoration-none">
                                        <?= esc($post['title']) ?>
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <?= date('M j, Y', strtotime($post['created_at'])) ?> • 
                                    <span class="badge bg-<?= $post['status'] === 'published' ? 'success' : 'warning' ?>">
                                        <?= ucfirst($post['status']) ?>
                                    </span>
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent activity found</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
            </div>
            <div class="admin-card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/users/edit/' . $profile_user['id']) ?>" class="btn btn-admin-primary w-100">
                            <i class="fas fa-edit me-2"></i>Edit User
                        </a>
                    </div>
                    
                    <?php if ($profile_user['status'] === 'active'): ?>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-warning w-100" onclick="toggleUserStatus(<?= $profile_user['id'] ?>, 'inactive')">
                            <i class="fas fa-pause me-2"></i>Deactivate
                        </button>
                    </div>
                    <?php else: ?>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-success w-100" onclick="toggleUserStatus(<?= $profile_user['id'] ?>, 'active')">
                            <i class="fas fa-play me-2"></i>Activate
                        </button>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!$profile_user['email_verified_at']): ?>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-info w-100" onclick="verifyUserEmail(<?= $profile_user['id'] ?>)">
                            <i class="fas fa-check me-2"></i>Verify Email
                        </button>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($profile_user['id'] != $user['id']): ?>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-danger w-100" onclick="deleteUser(<?= $profile_user['id'] ?>)">
                            <i class="fas fa-trash me-2"></i>Delete User
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function toggleUserStatus(userId, newStatus) {
        if (confirm(`Are you sure you want to ${newStatus === 'active' ? 'activate' : 'deactivate'} this user?`)) {
            adminAPI(`/admin/users/toggle-status/${userId}`, 'POST')
                .then(response => {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert(response.message, 'error');
                    }
                });
        }
    }

    function verifyUserEmail(userId) {
        if (confirm('Are you sure you want to verify this user\'s email?')) {
            adminAPI(`/admin/users/verify-email/${userId}`, 'POST')
                .then(response => {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert(response.message, 'error');
                    }
                });
        }
    }

    function deleteUser(userId) {
        if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            adminAPI(`/admin/users/delete/${userId}`, 'DELETE')
                .then(response => {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        setTimeout(() => window.location.href = '<?= base_url('admin/users') ?>', 1000);
                    } else {
                        showAlert(response.message, 'error');
                    }
                });
        }
    }
</script>
<?= $this->endSection() ?>
