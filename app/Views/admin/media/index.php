<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Media Library<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<span>Media Library</span>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Media Library</h1>
        <p class="page-subtitle">Manage your images, videos, documents and other media files</p>
    </div>
    <div class="page-actions">
        <div class="dropdown">
            <button class="btn btn-admin-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-filter me-2"></i>Filter by Type
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" data-type="all">All Files</a></li>
                <li><a class="dropdown-item" href="#" data-type="image">Images</a></li>
                <li><a class="dropdown-item" href="#" data-type="video">Videos</a></li>
                <li><a class="dropdown-item" href="#" data-type="audio">Audio</a></li>
                <li><a class="dropdown-item" href="#" data-type="document">Documents</a></li>
                <li><a class="dropdown-item" href="#" data-type="archive">Archives</a></li>
                <li><a class="dropdown-item" href="#" data-type="other">Other</a></li>
            </ul>
        </div>
        <button type="button" class="btn btn-admin-primary" onclick="openUploadModal()">
            <i class="fas fa-upload me-2"></i>Upload Files
        </button>
    </div>
</div>

<!-- Media Statistics -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon bg-primary">
            <i class="fas fa-folder"></i>
        </div>
        <div class="stat-value"><?= number_format($stats['total_files']) ?></div>
        <div class="stat-label">Total Files</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon bg-info">
            <i class="fas fa-hdd"></i>
        </div>
        <div class="stat-value"><?= \App\Models\MediaModel::formatFileSize($stats['total_size']) ?></div>
        <div class="stat-label">Total Size</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon bg-success">
            <i class="fas fa-image"></i>
        </div>
        <div class="stat-value"><?= number_format($stats['images']) ?></div>
        <div class="stat-label">Images</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon bg-warning">
            <i class="fas fa-video"></i>
        </div>
        <div class="stat-value"><?= number_format($stats['videos']) ?></div>
        <div class="stat-label">Videos</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon bg-danger">
            <i class="fas fa-file-alt"></i>
        </div>
        <div class="stat-value"><?= number_format($stats['documents']) ?></div>
        <div class="stat-label">Documents</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon bg-secondary">
            <i class="fas fa-file"></i>
        </div>
        <div class="stat-value"><?= number_format($stats['other']) ?></div>
        <div class="stat-label">Other</div>
    </div>
</div>

<!-- Search and Bulk Actions -->
<div class="admin-card mb-3">
    <div class="admin-card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="admin-form-control" id="mediaSearch" placeholder="Search media files...">
                    <button class="btn btn-admin-secondary" type="button" onclick="searchMedia()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-outline-danger" onclick="bulkDelete()" id="bulkDeleteBtn" style="display: none;">
                        <i class="fas fa-trash me-2"></i>Delete Selected
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-admin-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-2"></i>Tools
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="generateThumbnails()">
                                <i class="fas fa-image me-2"></i>Generate Thumbnails
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="cleanupMedia()">
                                <i class="fas fa-broom me-2"></i>Cleanup Unused
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Media Grid -->
<div class="admin-card">
    <div class="admin-card-body">
        <div id="mediaGrid" class="row g-3">
            <!-- Media items will be loaded here -->
        </div>

        <div id="loadingSpinner" class="text-center py-4" style="display: none;">
            <div class="loading-spinner"></div>
        </div>

        <div id="emptyState" class="admin-empty-state" style="display: none;">
            <i class="fas fa-folder-open"></i>
            <h4>No media files found</h4>
            <p>Upload some files to get started</p>
            <button type="button" class="btn btn-admin-primary" onclick="openUploadModal()">
                <i class="fas fa-upload me-2"></i>Upload Files
            </button>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Files</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="admin-upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h5>Drag & Drop Files Here</h5>
                    <p class="text-muted">or click to browse</p>
                    <input type="file" id="fileInput" multiple accept="*/*" style="display: none;">
                    <button type="button" class="btn btn-admin-primary" onclick="document.getElementById('fileInput').click()">
                        Choose Files
                    </button>
                </div>
                
                <div id="uploadProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted" id="uploadStatus">Uploading...</small>
                    </div>
                </div>
                
                <div id="uploadResults" class="mt-3" style="display: none;">
                    <h6>Upload Results:</h6>
                    <div id="uploadResultsList"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-admin-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-admin-primary" onclick="startUpload()" id="uploadBtn" style="display: none;">
                    Upload Files
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Media Details Modal -->
<div class="modal fade" id="mediaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Media Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="mediaModalBody">
                <!-- Media details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-admin-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-admin-primary" onclick="saveMediaDetails()" id="saveMediaBtn">
                    Save Changes
                </button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let selectedMedia = [];
let currentFilter = 'all';

$(document).ready(function() {
    loadMedia();
    
    // Setup file input change handler
    $('#fileInput').on('change', function() {
        const files = this.files;
        if (files.length > 0) {
            $('#uploadBtn').show();
            updateUploadArea(files);
        }
    });
    
    // Setup drag and drop
    setupDragAndDrop();
    
    // Setup search
    $('#mediaSearch').on('keyup', debounce(searchMedia, 300));
    
    // Filter handlers
    $('[data-type]').on('click', function(e) {
        e.preventDefault();
        currentFilter = $(this).data('type');
        loadMedia();
    });
});

function loadMedia() {
    $('#loadingSpinner').show();
    $('#mediaGrid').empty();
    
    $.get('<?= base_url('admin/media/get-media') ?>', {
        type: currentFilter,
        limit: 50
    })
    .done(function(data) {
        displayMedia(data);
    })
    .fail(function() {
        showAlert('Failed to load media files', 'error');
    })
    .always(function() {
        $('#loadingSpinner').hide();
    });
}

function displayMedia(mediaItems) {
    const grid = $('#mediaGrid');
    grid.empty();
    
    if (mediaItems.length === 0) {
        $('#emptyState').show();
        return;
    }
    
    $('#emptyState').hide();
    
    mediaItems.forEach(function(item) {
        const mediaCard = createMediaCard(item);
        grid.append(mediaCard);
    });
}

function createMediaCard(item) {
    const isImage = item.file_type === 'image';
    const thumbnail = isImage ? item.thumbnail_url || item.file_url : getFileIcon(item.file_type);
    
    return `
        <div class="col-md-3 col-sm-4 col-6">
            <div class="card media-card" data-id="${item.id}">
                <div class="position-relative">
                    <input type="checkbox" class="form-check-input position-absolute top-0 start-0 m-2 media-checkbox" 
                           value="${item.id}" onchange="updateSelection()">
                    ${isImage ? 
                        `<img src="${thumbnail}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="${item.alt_text || item.original_name}">` :
                        `<div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 150px;">
                            <i class="${thumbnail} fa-3x text-muted"></i>
                         </div>`
                    }
                </div>
                <div class="card-body p-2">
                    <h6 class="card-title small mb-1" title="${item.original_name}">${truncateText(item.original_name, 20)}</h6>
                    <small class="text-muted d-block">${item.formatted_size}</small>
                    <small class="text-muted d-block">${formatDate(item.created_at)}</small>
                </div>
                <div class="card-footer p-2">
                    <div class="btn-group w-100" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewMedia(${item.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyUrl('${item.file_url}')">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteMedia(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getFileIcon(fileType) {
    const icons = {
        'video': 'fas fa-video',
        'audio': 'fas fa-music',
        'document': 'fas fa-file-alt',
        'archive': 'fas fa-file-archive',
        'other': 'fas fa-file'
    };
    return icons[fileType] || 'fas fa-file';
}

function truncateText(text, length) {
    return text.length > length ? text.substring(0, length) + '...' : text;
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function updateSelection() {
    selectedMedia = $('.media-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selectedMedia.length > 0) {
        $('#bulkDeleteBtn').show();
    } else {
        $('#bulkDeleteBtn').hide();
    }
}

function searchMedia() {
    const query = $('#mediaSearch').val();

    $('#loadingSpinner').show();
    $('#mediaGrid').empty();

    $.get('<?= base_url('admin/media/get-media') ?>', {
        search: query,
        type: currentFilter,
        limit: 50
    })
    .done(function(data) {
        displayMedia(data);
    })
    .always(function() {
        $('#loadingSpinner').hide();
    });
}

function openUploadModal() {
    $('#uploadModal').modal('show');
}

function setupDragAndDrop() {
    const uploadArea = document.getElementById('uploadArea');

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    uploadArea.addEventListener('drop', handleDrop, false);

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        uploadArea.classList.add('border-primary');
    }

    function unhighlight(e) {
        uploadArea.classList.remove('border-primary');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        document.getElementById('fileInput').files = files;
        updateUploadArea(files);
        $('#uploadBtn').show();
    }
}

function updateUploadArea(files) {
    const uploadArea = $('#uploadArea');
    const fileCount = files.length;

    uploadArea.html(`
        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
        <h5>${fileCount} file(s) selected</h5>
        <p class="text-muted">Ready to upload</p>
    `);
}

function startUpload() {
    const fileInput = document.getElementById('fileInput');
    const files = fileInput.files;

    if (files.length === 0) {
        showAlert('Please select files to upload', 'warning');
        return;
    }

    const formData = new FormData();
    for (let i = 0; i < files.length; i++) {
        formData.append('files[]', files[i]);
    }

    $('#uploadProgress').show();
    $('#uploadBtn').hide();

    $.ajax({
        url: '<?= base_url('admin/media/upload') ?>',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(evt) {
                if (evt.lengthComputable) {
                    const percentComplete = (evt.loaded / evt.total) * 100;
                    $('.progress-bar').css('width', percentComplete + '%');
                    $('#uploadStatus').text(`Uploading... ${Math.round(percentComplete)}%`);
                }
            }, false);
            return xhr;
        },
        success: function(response) {
            $('#uploadProgress').hide();
            $('#uploadResults').show();

            let resultsHtml = '';
            if (response.uploaded && response.uploaded.length > 0) {
                resultsHtml += '<div class="alert alert-success">Successfully uploaded:</div>';
                response.uploaded.forEach(function(file) {
                    resultsHtml += `<div class="text-success"><i class="fas fa-check me-2"></i>${file.original_name}</div>`;
                });
            }

            if (response.errors && response.errors.length > 0) {
                resultsHtml += '<div class="alert alert-danger mt-2">Errors:</div>';
                response.errors.forEach(function(error) {
                    resultsHtml += `<div class="text-danger"><i class="fas fa-times me-2"></i>${error}</div>`;
                });
            }

            $('#uploadResultsList').html(resultsHtml);

            // Reload media grid
            loadMedia();

            // Reset form
            setTimeout(function() {
                $('#uploadModal').modal('hide');
                resetUploadModal();
            }, 2000);
        },
        error: function() {
            $('#uploadProgress').hide();
            showAlert('Upload failed', 'error');
        }
    });
}

function resetUploadModal() {
    $('#fileInput').val('');
    $('#uploadProgress').hide();
    $('#uploadResults').hide();
    $('#uploadBtn').hide();
    $('#uploadArea').html(`
        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
        <h5>Drag & Drop Files Here</h5>
        <p class="text-muted">or click to browse</p>
        <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
            Choose Files
        </button>
    `);
}

function viewMedia(id) {
    $.get(`<?= base_url('admin/media/details') ?>/${id}`)
    .done(function(response) {
        if (response.success) {
            showMediaDetails(response.media);
        } else {
            showAlert(response.message, 'error');
        }
    })
    .fail(function() {
        showAlert('Failed to load media details', 'error');
    });
}

function showMediaDetails(media) {
    const isImage = media.file_type === 'image';

    const modalBody = `
        <div class="row">
            <div class="col-md-6">
                ${isImage ?
                    `<img src="${media.file_url}" class="img-fluid rounded" alt="${media.alt_text || media.original_name}">` :
                    `<div class="d-flex align-items-center justify-content-center bg-light rounded" style="height: 300px;">
                        <i class="${getFileIcon(media.file_type)} fa-5x text-muted"></i>
                     </div>`
                }
            </div>
            <div class="col-md-6">
                <form id="mediaDetailsForm">
                    <input type="hidden" id="mediaId" value="${media.id}">

                    <div class="mb-3">
                        <label class="form-label">File Name</label>
                        <input type="text" class="form-control" value="${media.original_name}" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">File Size</label>
                        <input type="text" class="form-control" value="${media.formatted_size}" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">File Type</label>
                        <input type="text" class="form-control" value="${media.mime_type}" readonly>
                    </div>

                    ${media.width && media.height ? `
                    <div class="mb-3">
                        <label class="form-label">Dimensions</label>
                        <input type="text" class="form-control" value="${media.width} × ${media.height} pixels" readonly>
                    </div>
                    ` : ''}

                    <div class="mb-3">
                        <label class="form-label">Alt Text</label>
                        <input type="text" class="form-control" id="altText" value="${media.alt_text || ''}" placeholder="Alternative text for accessibility">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Caption</label>
                        <input type="text" class="form-control" id="caption" value="${media.caption || ''}" placeholder="Image caption">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" id="description" rows="3" placeholder="File description">${media.description || ''}</textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">File URL</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="${media.file_url}" readonly>
                            <button type="button" class="btn btn-outline-secondary" onclick="copyUrl('${media.file_url}')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">
                            Uploaded by ${media.first_name} ${media.last_name} on ${formatDate(media.created_at)}
                        </small>
                    </div>
                </form>
            </div>
        </div>
    `;

    $('#mediaModalBody').html(modalBody);
    $('#mediaModal').modal('show');
}

function saveMediaDetails() {
    const id = $('#mediaId').val();
    const data = {
        alt_text: $('#altText').val(),
        caption: $('#caption').val(),
        description: $('#description').val()
    };

    $.post(`<?= base_url('admin/media/update') ?>/${id}`, data)
    .done(function(response) {
        if (response.success) {
            showAlert('Media updated successfully', 'success');
            $('#mediaModal').modal('hide');
            loadMedia();
        } else {
            showAlert(response.message, 'error');
        }
    })
    .fail(function() {
        showAlert('Failed to update media', 'error');
    });
}

function copyUrl(url) {
    navigator.clipboard.writeText(url).then(function() {
        showAlert('URL copied to clipboard', 'success');
    });
}

function deleteMedia(id) {
    confirmDelete('this media file', function() {
        adminAPI(`<?= base_url('admin/media/delete') ?>/${id}`, {
            method: 'DELETE'
        })
        .then(data => {
            if (data.success) {
                showAlert(data.message || 'Media file deleted successfully', 'success');
                loadMedia();
            } else {
                showAlert(data.message || 'Failed to delete media file', 'error');
            }
        });
    });
}

function bulkDelete() {
    if (selectedMedia.length === 0) {
        showAlert('Please select media files to delete', 'warning');
        return;
    }

    confirmBulkAction('delete', selectedMedia.length, function() {
        adminAPI('<?= base_url('admin/media/bulk-delete') ?>', {
            method: 'POST',
            body: JSON.stringify({
                media_ids: selectedMedia
            })
        })
        .then(data => {
            if (data.success) {
                showAlert(data.message || `${selectedMedia.length} media file(s) deleted successfully`, 'success');
                loadMedia();
                selectedMedia = [];
                $('#bulkDeleteBtn').hide();
            } else {
                showAlert(data.message || 'Failed to delete media files', 'error');
            }
        });
    });
}

function generateThumbnails() {
    if (confirm('Generate thumbnails for all images? This may take a while.')) {
        $.post('<?= base_url('admin/media/generate-thumbnails') ?>')
        .done(function(response) {
            showAlert(response.message, response.success ? 'success' : 'warning');
        })
        .fail(function() {
            showAlert('Failed to generate thumbnails', 'error');
        });
    }
}

function cleanupMedia() {
    if (confirm('Remove unused media files? This action cannot be undone.')) {
        $.post('<?= base_url('admin/media/cleanup') ?>')
        .done(function(response) {
            showAlert(response.message, response.success ? 'success' : 'info');
            if (response.success) {
                loadMedia();
            }
        })
        .fail(function() {
            showAlert('Failed to cleanup media', 'error');
        });
    }
}

function showAlert(message, type) {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const alert = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('body').append(alert);

    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
<?= $this->endSection() ?>
