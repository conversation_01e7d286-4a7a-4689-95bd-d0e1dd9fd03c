<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Blog Posts<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<span>Blog Posts</span>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Blog Posts</h1>
        <p class="page-subtitle">Manage your blog content and articles</p>
    </div>
    <div class="page-actions">
        <div class="dropdown">
            <button class="btn btn-admin-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-filter me-2"></i>Filter by Status
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" data-status="all">All Posts</a></li>
                <li><a class="dropdown-item" href="#" data-status="published">Published</a></li>
                <li><a class="dropdown-item" href="#" data-status="draft">Draft</a></li>
                <li><a class="dropdown-item" href="#" data-status="private">Private</a></li>
                <li><a class="dropdown-item" href="#" data-status="scheduled">Scheduled</a></li>
            </ul>
        </div>
        <a href="<?= base_url('admin/blog/create') ?>" class="btn btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Create New Post
        </a>
    </div>
</div>

<!-- Bulk Actions -->
<div class="admin-card mb-3" id="bulkActionsCard" style="display: none;">
    <div class="admin-card-body py-2">
        <div class="d-flex align-items-center gap-3">
            <span class="text-admin-secondary">
                <span id="selectedCount">0</span> posts selected
            </span>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-success" onclick="bulkAction('publish')">
                    <i class="fas fa-eye me-1"></i>Publish
                </button>
                <button type="button" class="btn btn-sm btn-warning" onclick="bulkAction('draft')">
                    <i class="fas fa-edit me-1"></i>Move to Draft
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="bulkAction('delete')">
                    <i class="fas fa-trash me-1"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Posts Table -->
<div class="admin-table">
    <table id="postsTable" class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th width="30">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </th>
                        <th>Title</th>
                        <th>Author</th>
                        <th>Category</th>
                        <th>Status</th>
                        <th>Views</th>
                        <th>Comments</th>
                        <th>Published</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($posts as $post): ?>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input post-checkbox" value="<?= $post['id'] ?>">
                        </td>
                        <td>
                            <div>
                                <strong><?= esc($post['title']) ?></strong>
                                <?php if (!empty($post['excerpt'])): ?>
                                <br><small class="text-muted"><?= esc(substr($post['excerpt'], 0, 100)) ?>...</small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-primary rounded-circle">
                                        <?= strtoupper(substr($post['first_name'] ?? $post['username'], 0, 1)) ?>
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-medium"><?= esc($post['first_name'] . ' ' . $post['last_name']) ?></div>
                                    <small class="text-muted">@<?= esc($post['username']) ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <?php if (!empty($post['category_name'])): ?>
                                <span class="badge bg-info"><?= esc($post['category_name']) ?></span>
                            <?php else: ?>
                                <span class="text-muted">Uncategorized</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $statusColors = [
                                'published' => 'success',
                                'draft' => 'secondary',
                                'private' => 'warning',
                                'scheduled' => 'info'
                            ];
                            $statusColor = $statusColors[$post['status']] ?? 'secondary';
                            ?>
                            <span class="status-badge <?= $post['status'] ?>"><?= ucfirst($post['status']) ?></span>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">
                                <i class="fas fa-eye me-1"></i><?= number_format($post['view_count']) ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">
                                <i class="fas fa-comments me-1"></i><?= number_format($post['comment_count']) ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($post['published_at']): ?>
                                <small class="text-muted">
                                    <?= date('M j, Y', strtotime($post['published_at'])) ?><br>
                                    <?= date('g:i A', strtotime($post['published_at'])) ?>
                                </small>
                            <?php else: ?>
                                <span class="text-muted">—</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?= base_url('admin/blog/edit/' . $post['id']) ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-secondary"
                                        onclick="duplicatePost(<?= $post['id'] ?>, '<?= esc($post['title']) ?>')" title="Duplicate">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="deletePost(<?= $post['id'] ?>, '<?= esc($post['title']) ?>')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#postsTable').DataTable({
        responsive: true,
        order: [[7, 'desc']], // Sort by published date
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: [0, 8] } // Disable sorting for checkbox and actions
        ],
        language: {
            search: "Search posts:",
            lengthMenu: "Show _MENU_ posts per page",
            info: "Showing _START_ to _END_ of _TOTAL_ posts",
        }
    });

    // Handle select all checkbox
    $('#selectAll').change(function() {
        $('.post-checkbox').prop('checked', this.checked);
        updateBulkActions();
    });

    // Handle individual checkboxes
    $(document).on('change', '.post-checkbox', function() {
        updateBulkActions();
        
        // Update select all checkbox
        const totalCheckboxes = $('.post-checkbox').length;
        const checkedCheckboxes = $('.post-checkbox:checked').length;
        $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Handle status filter
    $('[data-status]').click(function(e) {
        e.preventDefault();
        const status = $(this).data('status');
        filterByStatus(status);
    });
});

function updateBulkActions() {
    const checkedCount = $('.post-checkbox:checked').length;
    $('#selectedCount').text(checkedCount);
    
    if (checkedCount > 0) {
        $('#bulkActionsCard').show();
    } else {
        $('#bulkActionsCard').hide();
    }
}

function deletePost(id, title = 'this post') {
    confirmDelete(title, function() {
        adminAPI(`<?= base_url('admin/blog/delete') ?>/${id}`, {
            method: 'DELETE'
        })
        .then(data => {
            if (data.success) {
                showAlert(data.message || 'Post deleted successfully', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message || 'Failed to delete post', 'error');
            }
        });
    });
}

function duplicatePost(id, title = 'this post') {
    confirmAction(`Are you sure you want to duplicate "${title}"?`, function() {
        adminAPI(`<?= base_url('admin/blog/duplicate') ?>/${id}`, {
            method: 'POST'
        })
        .then(data => {
            if (data.success) {
                showAlert(data.message || 'Post duplicated successfully', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message || 'Failed to duplicate post', 'error');
            }
        });
    }, {
        title: 'Duplicate Post',
        confirmText: 'Duplicate',
        type: 'info'
    });
}

function bulkAction(action) {
    const selectedIds = $('.post-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selectedIds.length === 0) {
        showAlert('Please select at least one post', 'warning');
        return;
    }

    confirmBulkAction(action, selectedIds.length, function() {
        adminAPI('<?= base_url('admin/blog/bulk-action') ?>', {
            method: 'POST',
            body: JSON.stringify({
                action: action,
                post_ids: selectedIds
            })
        })
        .then(data => {
            if (data.success) {
                showAlert(data.message || `${selectedIds.length} post(s) ${action}ed successfully`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message || 'Failed to perform bulk action', 'error');
            }
        });
    });
}

function filterByStatus(status) {
    // This would typically reload the page with a status filter
    // For now, we'll just reload the page
    if (status === 'all') {
        window.location.href = '<?= base_url('admin/blog') ?>';
    } else {
        window.location.href = `<?= base_url('admin/blog') ?>?status=${status}`;
    }
}
</script>
<?= $this->endSection() ?>
