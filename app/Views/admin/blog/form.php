<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?><?= $post ? 'Edit Post' : 'Create New Post' ?><?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/blog') ?>">Blog Posts</a>
<i class="fas fa-chevron-right"></i>
<span><?= $post ? 'Edit Post' : 'Create New Post' ?></span>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title"><?= $post ? 'Edit Post' : 'Create New Post' ?></h1>
        <p class="page-subtitle">Create and manage your blog content</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/blog') ?>" class="btn btn-admin-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Posts
        </a>
    </div>
</div>

<!-- Post Form -->
<form action="<?= $post ? base_url('admin/blog/update/' . $post['id']) : base_url('admin/blog/store') ?>" 
      method="post" id="postForm">
    
    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Post Content
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="title" class="admin-form-label">Title *</label>
                        <input type="text" class="admin-form-control" id="title" name="title"
                               value="<?= old('title', $post['title'] ?? '') ?>" required>
                        <div class="admin-form-text">The main title of your blog post</div>
                    </div>

                    <div class="admin-form-group">
                        <label for="slug" class="admin-form-label">URL Slug</label>
                        <input type="text" class="admin-form-control" id="slug" name="slug"
                               value="<?= old('slug', $post['slug'] ?? '') ?>">
                        <div class="admin-form-text">
                            <strong>URL:</strong>
                            <span class="text-muted"><?= base_url('blog/') ?></span><span id="slugPreview"><?= $post['slug'] ?? 'post-slug' ?></span>
                        </div>
                    </div>

                    <div class="admin-form-group">
                        <label for="excerpt" class="admin-form-label">Excerpt</label>
                        <textarea class="admin-form-control" id="excerpt" name="excerpt" rows="3"><?= old('excerpt', $post['excerpt'] ?? '') ?></textarea>
                        <div class="admin-form-text">Brief description of the post (optional)</div>
                    </div>

                    <div class="admin-form-group mb-0">
                        <label for="content" class="admin-form-label">Content *</label>
                        <textarea class="admin-form-control" id="content" name="content" rows="15" required><?= old('content', $post['content'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- SEO Settings -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>SEO Settings
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="meta_title" class="admin-form-label">Meta Title</label>
                        <input type="text" class="admin-form-control" id="meta_title" name="meta_title"
                               value="<?= old('meta_title', $post['meta_title'] ?? '') ?>" maxlength="255">
                        <div class="admin-form-text">SEO title for search engines (leave empty to use post title)</div>
                    </div>

                    <div class="admin-form-group">
                        <label for="meta_description" class="admin-form-label">Meta Description</label>
                        <textarea class="admin-form-control" id="meta_description" name="meta_description"
                                  rows="3" maxlength="500"><?= old('meta_description', $post['meta_description'] ?? '') ?></textarea>
                        <div class="admin-form-text">Brief description for search engines (150-160 characters recommended)</div>
                    </div>

                    <div class="admin-form-group mb-0">
                        <label for="meta_keywords" class="admin-form-label">Meta Keywords</label>
                        <input type="text" class="admin-form-control" id="meta_keywords" name="meta_keywords"
                               value="<?= old('meta_keywords', $post['meta_keywords'] ?? '') ?>">
                        <div class="admin-form-text">Comma-separated keywords for SEO</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Publish Settings -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Publish Settings
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="status" class="admin-form-label">Status *</label>
                        <select class="admin-form-control" id="status" name="status" required>
                            <option value="draft" <?= old('status', $post['status'] ?? 'draft') === 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="published" <?= old('status', $post['status'] ?? '') === 'published' ? 'selected' : '' ?>>Published</option>
                            <option value="private" <?= old('status', $post['status'] ?? '') === 'private' ? 'selected' : '' ?>>Private</option>
                            <option value="scheduled" <?= old('status', $post['status'] ?? '') === 'scheduled' ? 'selected' : '' ?>>Scheduled</option>
                        </select>
                    </div>

                    <div class="admin-form-group" id="publishedAtGroup" style="display: none;">
                        <label for="published_at" class="admin-form-label">Publish Date & Time</label>
                        <input type="datetime-local" class="admin-form-control" id="published_at" name="published_at"
                               value="<?= old('published_at', $post['published_at'] ? date('Y-m-d\TH:i', strtotime($post['published_at'])) : '') ?>">
                    </div>

                    <div class="admin-form-group">
                        <label for="category_id" class="admin-form-label">Category</label>
                        <select class="admin-form-control" id="category_id" name="category_id">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?= $category['id'] ?>"
                                    <?= old('category_id', $post['category_id'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                                <?= esc($category['name']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="admin-form-group">
                        <label for="tags" class="admin-form-label">Tags</label>
                        <input type="text" class="admin-form-control" id="tags" name="tags"
                               value="<?= old('tags', isset($post['tags']) ? implode(', ', $post['tags']) : '') ?>"
                               placeholder="Enter tags separated by commas">
                        <div class="admin-form-text">Separate multiple tags with commas</div>
                    </div>

                    <div class="admin-form-group">
                        <label for="featured_image" class="admin-form-label">Featured Image</label>
                        <input type="text" class="admin-form-control" id="featured_image" name="featured_image"
                               value="<?= old('featured_image', $post['featured_image'] ?? '') ?>"
                               placeholder="Image URL or path">
                        <div class="admin-form-text">URL or path to the featured image</div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-admin-primary">
                            <i class="fas fa-save me-2"></i>
                            <?= $post ? 'Update Post' : 'Create Post' ?>
                        </button>

                        <?php if ($post): ?>
                        <a href="<?= base_url('blog/' . $post['slug']) ?>" class="btn btn-admin-secondary" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>Preview Post
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Post Statistics (for edit mode) -->
            <?php if ($post): ?>
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Post Statistics
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-admin-primary"><?= number_format($post['view_count']) ?></h4>
                                <small class="text-muted">Views</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-admin-success"><?= number_format($post['comment_count']) ?></h4>
                            <small class="text-muted">Comments</small>
                        </div>
                    </div>

                    <hr>

                    <div class="small text-muted">
                        <div class="mb-1">
                            <strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($post['created_at'])) ?>
                        </div>
                        <div class="mb-1">
                            <strong>Updated:</strong> <?= date('M j, Y g:i A', strtotime($post['updated_at'])) ?>
                        </div>
                        <?php if ($post['reading_time']): ?>
                        <div>
                            <strong>Reading Time:</strong> <?= $post['reading_time'] ?> min
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    // Auto-generate slug from title
    $('#title').on('input', function() {
        const title = $(this).val();
        const slug = title.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
        
        if (!$('#slug').data('manual-edit')) {
            $('#slug').val(slug);
            $('#slugPreview').text(slug || 'post-slug');
        }
    });

    // Track manual slug editing
    $('#slug').on('input', function() {
        $(this).data('manual-edit', true);
        $('#slugPreview').text($(this).val() || 'post-slug');
    });

    // Show/hide published date field based on status
    $('#status').on('change', function() {
        if ($(this).val() === 'scheduled') {
            $('#publishedAtGroup').show();
        } else {
            $('#publishedAtGroup').hide();
        }
    });

    // Initialize published date field visibility
    $('#status').trigger('change');

    // Initialize rich text editor for content
    if (typeof tinymce !== 'undefined') {
        tinymce.init({
            selector: '#content',
            height: 400,
            plugins: 'advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code fullscreen insertdatetime media table paste code help wordcount',
            toolbar: 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }'
        });
    }

    // Form validation
    $('#postForm').on('submit', function(e) {
        const title = $('#title').val().trim();
        const content = $('#content').val().trim();
        
        if (!title) {
            e.preventDefault();
            alert('Please enter a post title.');
            $('#title').focus();
            return false;
        }
        
        if (!content) {
            e.preventDefault();
            alert('Please enter post content.');
            $('#content').focus();
            return false;
        }
    });
});
</script>
<?= $this->endSection() ?>
