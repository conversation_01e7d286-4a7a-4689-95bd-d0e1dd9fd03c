<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Create Template<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/themes') ?>">Themes</a>
<i class="fas fa-chevron-right"></i>
<span>Create Template</span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" rel="stylesheet">
<style>
    .CodeMirror {
        border: 1px solid #ddd;
        border-radius: 4px;
        height: 400px;
    }
    .template-preview {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        background: #f8f9fa;
        min-height: 200px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Create New Template</h1>
        <p class="page-subtitle">Design custom templates for your website</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/themes') ?>" class="btn btn-admin-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Themes
        </a>
    </div>
</div>

<form action="<?= base_url('admin/themes/store') ?>" method="post" enctype="multipart/form-data">
    <?= csrf_field() ?>

    <div class="row">
        <div class="col-md-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Template Information
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="name" class="admin-form-label">Template Name *</label>
                                <input type="text" class="admin-form-control" id="name" name="name"
                                       value="<?= old('name') ?>" required>
                                <div class="admin-form-text">Enter a descriptive name for your template</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="type" class="admin-form-label">Template Type *</label>
                                <select class="admin-form-control" id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="page" <?= old('type') === 'page' ? 'selected' : '' ?>>Page Template</option>
                                    <option value="blog" <?= old('type') === 'blog' ? 'selected' : '' ?>>Blog Template</option>
                                    <option value="layout" <?= old('type') === 'layout' ? 'selected' : '' ?>>Layout Template</option>
                                </select>
                                <div class="admin-form-text">Choose the type of template you're creating</div>
                            </div>
                        </div>
                    </div>

                    <div class="admin-form-group">
                        <label for="file_path" class="admin-form-label">File Path *</label>
                        <input type="text" class="admin-form-control" id="file_path" name="file_path"
                               value="<?= old('file_path') ?>" placeholder="templates/custom/my-template.php" required>
                        <div class="admin-form-text">Path relative to app/Views/ directory (e.g., templates/custom/my-template.php)</div>
                    </div>

                    <div class="admin-form-group mb-0">
                        <label for="description" class="admin-form-label">Description</label>
                        <textarea class="admin-form-control" id="description" name="description" rows="3"><?= old('description') ?></textarea>
                        <div class="admin-form-text">Optional description of what this template is for</div>
                    </div>
                </div>
            </div>

                            <!-- Template Content -->
                            <div class="admin-card mb-4">
                                <div class="admin-card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-code me-2"></i>Template Content
                                    </h5>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-admin-secondary" onclick="loadTemplate('basic')">
                                            Basic Template
                                        </button>
                                        <button type="button" class="btn btn-admin-secondary" onclick="loadTemplate('blog')">
                                            Blog Template
                                        </button>
                                        <button type="button" class="btn btn-admin-secondary" onclick="loadTemplate('page')">
                                            Page Template
                                        </button>
                                    </div>
                                </div>
                                <div class="admin-card-body">
                                    <textarea id="template_content" name="template_content"><?= old('template_content') ?></textarea>
                                    <div class="admin-form-text mt-2">
                                        Write your template code here. You can use CodeIgniter view syntax and PHP.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Preview Image -->
                            <div class="admin-card mb-4">
                                <div class="admin-card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-image me-2"></i>Preview Image
                                    </h5>
                                </div>
                                <div class="admin-card-body">
                                    <div class="admin-form-group">
                                        <label for="preview_image" class="admin-form-label">Upload Preview</label>
                                        <input type="file" class="admin-form-control" id="preview_image" name="preview_image"
                                               accept="image/*" onchange="previewImage(this)">
                                        <div class="admin-form-text">Upload a screenshot or preview of your template</div>
                                    </div>

                                    <div id="image_preview" class="template-preview text-center" style="display: none;">
                                        <img id="preview_img" src="" alt="Preview" class="img-fluid rounded">
                                    </div>

                                    <div id="no_preview" class="template-preview text-center text-muted">
                                        <i class="fas fa-image fa-3x mb-2"></i>
                                        <p>No preview image selected</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Template Variables -->
                            <div class="admin-card">
                                <div class="admin-card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-code me-2"></i>Available Variables
                                    </h5>
                                </div>
                                <div class="admin-card-body">
                                    <div class="admin-form-text">
                                        <strong>Common variables you can use:</strong><br>
                                        <code>$title</code> - Page title<br>
                                        <code>$content</code> - Main content<br>
                                        <code>$meta_description</code> - Meta description<br>
                                        <code>$meta_keywords</code> - Meta keywords<br>
                                        <code>$user</code> - Current user data<br>
                                        <code>$settings</code> - Site settings<br>
                                        <br>
                                        <strong>Blog specific:</strong><br>
                                        <code>$post</code> - Blog post data<br>
                                        <code>$category</code> - Post category<br>
                                        <code>$tags</code> - Post tags<br>
                                        <code>$author</code> - Post author<br>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <button type="button" class="btn btn-admin-secondary" onclick="previewTemplate()">
                                    <i class="fas fa-eye me-2"></i>Preview
                                </button>
                                <button type="submit" class="btn btn-admin-primary">
                                    <i class="fas fa-save me-2"></i>Create Template
                                </button>
                            </div>
                        </div>
                    </div>
</form>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This is a preview with sample data. Your actual template will use real content from your CMS.
                </div>
                <iframe id="preview_frame" srcdoc="" style="width: 100%; height: 500px; border: 1px solid #ddd; border-radius: 4px;"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close Preview</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?php
// Define template starters as PHP variables
$basicTemplate = '<?= $this->extend(\'layouts/main\') ?>

<?= $this->section(\'title\') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section(\'content\') ?>
<div class="container">
    <h1><?= esc($title) ?></h1>
    <div class="content">
        <?= $content ?>
    </div>
</div>
<?= $this->endSection() ?>';

$blogTemplate = '<?= $this->extend(\'layouts/main\') ?>

<?= $this->section(\'title\') ?><?= esc($post[\'title\']) ?><?= $this->endSection() ?>

<?= $this->section(\'content\') ?>
<article class="blog-post">
    <header class="post-header">
        <h1><?= esc($post[\'title\']) ?></h1>
        <div class="post-meta">
            <span class="author">By <?= esc($author[\'first_name\'] . \' \' . $author[\'last_name\']) ?></span>
            <span class="date"><?= date(\'F j, Y\', strtotime($post[\'published_at\'])) ?></span>
            <?php if (!empty($category)): ?>
            <span class="category">in <?= esc($category[\'name\']) ?></span>
            <?php endif; ?>
        </div>
    </header>

    <div class="post-content">
        <?= $post[\'content\'] ?>
    </div>

    <?php if (!empty($tags)): ?>
    <div class="post-tags">
        <strong>Tags:</strong>
        <?php foreach ($tags as $tag): ?>
            <span class="tag"><?= esc($tag[\'name\']) ?></span>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
</article>
<?= $this->endSection() ?>';

$pageTemplate = '<?= $this->extend(\'layouts/main\') ?>

<?= $this->section(\'title\') ?><?= esc($page[\'title\']) ?><?= $this->endSection() ?>

<?= $this->section(\'meta\') ?>
<meta name="description" content="<?= esc($page[\'meta_description\']) ?>">
<meta name="keywords" content="<?= esc($page[\'meta_keywords\']) ?>">
<?= $this->endSection() ?>

<?= $this->section(\'content\') ?>
<div class="page-content">
    <header class="page-header">
        <h1><?= esc($page[\'title\']) ?></h1>
    </header>

    <div class="page-body">
        <?= $page[\'content\'] ?>
    </div>
</div>
<?= $this->endSection() ?>';
?>

<?= $this->section('scripts') ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/php/php.min.js"></script>

<script>
    // Initialize CodeMirror
    const editor = CodeMirror.fromTextArea(document.getElementById('template_content'), {
        lineNumbers: true,
        mode: 'application/x-httpd-php',
        theme: 'monokai',
        indentUnit: 4,
        lineWrapping: true,
        autoCloseTags: true,
        autoCloseBrackets: true
    });

    // Auto-generate file path from name
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const type = document.getElementById('type').value;
        if (name && type) {
            const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
            document.getElementById('file_path').value = `templates/${type}/${slug}.php`;
        }
    });

    document.getElementById('type').addEventListener('change', function() {
        const name = document.getElementById('name').value;
        const type = this.value;
        if (name && type) {
            const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
            document.getElementById('file_path').value = `templates/${type}/${slug}.php`;
        }
    });

    // Preview image function
    function previewImage(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('preview_img').src = e.target.result;
                document.getElementById('image_preview').style.display = 'block';
                document.getElementById('no_preview').style.display = 'none';
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    // Template content definitions (from PHP)
    const templates = {
        basic: <?= json_encode($basicTemplate) ?>,
        blog: <?= json_encode($blogTemplate) ?>,
        page: <?= json_encode($pageTemplate) ?>
    };

    // Load template starters
    function loadTemplate(type) {
        if (templates[type]) {
            editor.setValue(templates[type]);
        }
    }

    // Preview template function
    function previewTemplate() {
        const templateContent = editor.getValue();
        const templateType = document.getElementById('type').value;

        if (!templateContent.trim()) {
            alert('Please enter some template content first.');
            return;
        }

        if (!templateType) {
            alert('Please select a template type first.');
            return;
        }

        // Show loading
        const previewBtn = document.querySelector('[onclick="previewTemplate()"]');
        const originalText = previewBtn.innerHTML;
        previewBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Preview...';
        previewBtn.disabled = true;

        // Send AJAX request to preview endpoint
        fetch('<?= base_url('admin/themes/preview') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                'template_content': templateContent,
                'template_type': templateType,
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show preview in modal
                document.getElementById('preview_frame').srcdoc = data.html;
                const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
                previewModal.show();
            } else {
                alert('Preview failed: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Preview error:', error);
            alert('Failed to generate preview. Please check your template syntax.');
        })
        .finally(() => {
            // Restore button
            previewBtn.innerHTML = originalText;
            previewBtn.disabled = false;
        });
    }
</script>
<?= $this->endSection() ?>
