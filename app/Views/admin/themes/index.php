<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Theme Management<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<span>Theme Management</span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<style>
    .template-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: var(--admin-shadow);
        border-radius: 12px;
        overflow: hidden;
    }

    .template-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--admin-shadow-lg);
    }

    .template-preview {
        height: 200px;
        background: var(--admin-light);
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid var(--admin-border);
        position: relative;
        overflow: hidden;
    }

    .template-preview img {
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .template-card:hover .template-preview img {
        transform: scale(1.05);
    }

    .default-badge {
        position: absolute;
        top: 12px;
        right: 12px;
        z-index: 10;
        border-radius: 6px;
        font-weight: 600;
    }

    .template-actions {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Theme Management</h1>
        <p class="page-subtitle">Manage templates and customize your website appearance</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/templates/marketplace') ?>" class="btn btn-admin-success me-2">
            <i class="fas fa-store me-2"></i>Template Store
        </a>
        <a href="<?= base_url('admin/templates/bulk-manager') ?>" class="btn btn-admin-info me-2">
            <i class="fas fa-tasks me-2"></i>Bulk Manager
        </a>
        <a href="<?= base_url('admin/templates/analytics') ?>" class="btn btn-admin-warning me-2">
            <i class="fas fa-chart-line me-2"></i>Analytics
        </a>
        <a href="<?= base_url('admin/themes/settings') ?>" class="btn btn-admin-secondary me-2">
            <i class="fas fa-cog me-2"></i>Theme Settings
        </a>
        <?php if ($user['role'] === 'admin'): ?>
        <a href="<?= base_url('admin/themes/create') ?>" class="btn btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Create Template
        </a>
        <?php endif; ?>
    </div>
</div>
<!-- Current Theme Info -->
<div class="admin-card mb-4">
    <div class="admin-card-header">
        <h5 class="mb-0"><i class="fas fa-palette me-2"></i>Current Active Theme</h5>
    </div>
    <div class="admin-card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h6>Theme: <span class="text-admin-primary"><?= ucfirst($current_theme) ?></span></h6>
                <p class="text-muted mb-0">This is the currently active theme for your website.</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="<?= base_url('/') ?>" target="_blank" class="btn btn-admin-info">
                    <i class="fas fa-eye me-2"></i>Preview Site
                </a>
            </div>
        </div>
    </div>
</div>
<!-- Templates Grid -->
<div class="row">
    <?php if (empty($templates)): ?>
        <div class="col-12">
            <div class="admin-empty-state">
                <i class="fas fa-paint-brush"></i>
                <h4>No Templates Found</h4>
                <p>Create your first template to get started with theme customization.</p>
                <?php if ($user['role'] === 'admin'): ?>
                <a href="<?= base_url('admin/themes/create') ?>" class="btn btn-admin-primary">
                    <i class="fas fa-plus me-2"></i>Create Template
                </a>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($templates as $template): ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="admin-card template-card position-relative">
                <?php if ($template['is_default']): ?>
                <span class="badge bg-admin-success default-badge">Default</span>
                <?php endif; ?>

                <div class="template-preview">
                    <?php if ($template['preview_image']): ?>
                        <img src="<?= base_url($template['preview_image']) ?>" alt="<?= esc($template['name']) ?>">
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="fas fa-image fa-3x mb-2"></i>
                            <p>No Preview</p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="admin-card-body">
                    <h5 class="mb-2"><?= esc($template['name']) ?></h5>
                    <p class="text-muted small mb-2">
                        Type: <span class="badge bg-admin-secondary"><?= ucfirst($template['type']) ?></span>
                    </p>
                    <?php if ($template['description']): ?>
                    <p class="text-muted mb-2"><?= esc($template['description']) ?></p>
                    <?php endif; ?>
                    <p class="mb-0">
                        <small class="text-muted">
                            Created by <?= esc($template['creator_name'] ?? 'Unknown') ?>
                        </small>
                    </p>
                </div>

                <div class="admin-card-footer template-actions">
                    <div class="btn-group w-100" role="group">
                        <a href="<?= base_url('admin/themes/edit/' . $template['id']) ?>"
                           class="btn btn-admin-secondary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>

                        <?php if (!$template['is_default']): ?>
                        <button type="button" class="btn btn-admin-success btn-sm"
                                onclick="setDefault(<?= $template['id'] ?>)">
                            <i class="fas fa-star"></i> Set Default
                        </button>

                        <?php if ($user['role'] === 'admin'): ?>
                        <button type="button" class="btn btn-admin-danger btn-sm"
                                onclick="deleteTemplate(<?= $template['id'] ?>)">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function setDefault(templateId) {
        confirmAction('Are you sure you want to set this template as default?', function() {
            adminAPI(`<?= base_url('admin/themes/set-default/') ?>${templateId}`, {
                method: 'POST'
            })
            .then(data => {
                if (data.success) {
                    showAlert(data.message || 'Template set as default successfully!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert(data.message || 'Failed to set template as default', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('An error occurred while setting template as default', 'error');
            });
        });
    }

    function deleteTemplate(templateId) {
        confirmDelete('Are you sure you want to delete this template? This action cannot be undone.', function() {
            adminAPI(`<?= base_url('admin/themes/delete/') ?>${templateId}`, {
                method: 'DELETE'
            })
            .then(data => {
                if (data.success) {
                    showAlert(data.message || 'Template deleted successfully!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert(data.message || 'Failed to delete template', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('An error occurred while deleting template', 'error');
            });
        });
    }
</script>
<?= $this->endSection() ?>
