<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Theme Settings<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/themes') ?>">Themes</a>
<i class="fas fa-chevron-right"></i>
<span>Settings</span>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>Theme Settings
                </h5>
            </div>
            <div class="card-body">
                <form action="<?= base_url('admin/themes/update-settings') ?>" method="post">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <?php if (!empty($settings)): ?>
                                <?php foreach ($settings as $group => $groupSettings): ?>
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0"><?= ucfirst(str_replace('_', ' ', $group)) ?> Settings</h6>
                                    </div>
                                    <div class="card-body">
                                        <?php foreach ($groupSettings as $key => $setting): ?>
                                        <div class="mb-3">
                                            <label for="<?= $key ?>" class="form-label">
                                                <?= ucfirst(str_replace('_', ' ', $key)) ?>
                                                <?php if (!empty($setting['description'])): ?>
                                                <small class="text-muted">(<?= esc($setting['description']) ?>)</small>
                                                <?php endif; ?>
                                            </label>
                                            
                                            <?php if ($setting['type'] === 'boolean'): ?>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="<?= $key ?>" name="settings[<?= $key ?>]" 
                                                           value="1" <?= $setting['value'] ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="<?= $key ?>">
                                                        Enable <?= ucfirst(str_replace('_', ' ', $key)) ?>
                                                    </label>
                                                </div>
                                            <?php elseif ($setting['type'] === 'number'): ?>
                                                <input type="number" class="form-control" 
                                                       id="<?= $key ?>" name="settings[<?= $key ?>]" 
                                                       value="<?= esc($setting['value']) ?>">
                                            <?php elseif ($setting['type'] === 'text'): ?>
                                                <textarea class="form-control" rows="3"
                                                          id="<?= $key ?>" name="settings[<?= $key ?>]"><?= esc($setting['value']) ?></textarea>
                                            <?php elseif ($setting['type'] === 'json'): ?>
                                                <textarea class="form-control" rows="5"
                                                          id="<?= $key ?>" name="settings[<?= $key ?>]"><?= json_encode($setting['value'], JSON_PRETTY_PRINT) ?></textarea>
                                                <div class="form-text">Enter valid JSON format</div>
                                            <?php else: ?>
                                                <input type="text" class="form-control" 
                                                       id="<?= $key ?>" name="settings[<?= $key ?>]" 
                                                       value="<?= esc($setting['value']) ?>">
                                            <?php endif; ?>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No theme settings found. You can add custom settings through the database or by creating templates that use specific settings.
                                </div>
                            <?php endif; ?>
                            
                            <!-- Add New Setting -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Add New Setting</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="new_key" class="form-label">Setting Key</label>
                                            <input type="text" class="form-control" id="new_key" name="new_setting[key]" 
                                                   placeholder="e.g., primary_color">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="new_type" class="form-label">Type</label>
                                            <select class="form-select" id="new_type" name="new_setting[type]">
                                                <option value="string">String</option>
                                                <option value="number">Number</option>
                                                <option value="boolean">Boolean</option>
                                                <option value="text">Text</option>
                                                <option value="json">JSON</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="new_value" class="form-label">Value</label>
                                            <input type="text" class="form-control" id="new_value" name="new_setting[value]" 
                                                   placeholder="Setting value">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="new_description" class="form-label">Description</label>
                                        <input type="text" class="form-control" id="new_description" name="new_setting[description]" 
                                               placeholder="Optional description">
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="new_public" name="new_setting[is_public]" value="1">
                                        <label class="form-check-label" for="new_public">
                                            Make this setting public (accessible in frontend)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <!-- Common Theme Settings -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Common Theme Settings</h6>
                                </div>
                                <div class="card-body">
                                    <small class="text-muted">
                                        <strong>Typical settings you might want to add:</strong><br><br>
                                        
                                        <strong>Colors:</strong><br>
                                        • primary_color<br>
                                        • secondary_color<br>
                                        • accent_color<br>
                                        • text_color<br><br>
                                        
                                        <strong>Layout:</strong><br>
                                        • container_width<br>
                                        • sidebar_position<br>
                                        • header_style<br>
                                        • footer_style<br><br>
                                        
                                        <strong>Typography:</strong><br>
                                        • font_family<br>
                                        • font_size<br>
                                        • line_height<br><br>
                                        
                                        <strong>Features:</strong><br>
                                        • show_breadcrumbs<br>
                                        • enable_dark_mode<br>
                                        • show_author_bio<br>
                                        • enable_comments<br>
                                    </small>
                                </div>
                            </div>
                            
                            <!-- Usage Instructions -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Using Settings in Templates</h6>
                                </div>
                                <div class="card-body">
                                    <small class="text-muted">
                                        <strong>In your templates, access settings like this:</strong><br><br>
                                        
                                        <code>&lt;?php $settings = service('settings'); ?&gt;</code><br>
                                        <code>&lt;?= $settings-&gt;getSetting('primary_color', '#007bff') ?&gt;</code><br><br>
                                        
                                        <strong>Or in CSS:</strong><br>
                                        <code>:root {</code><br>
                                        <code>&nbsp;&nbsp;--primary: &lt;?= $settings-&gt;getSetting('primary_color') ?&gt;;</code><br>
                                        <code>}</code><br><br>
                                        
                                        <strong>For boolean settings:</strong><br>
                                        <code>&lt;?php if ($settings-&gt;getSetting('show_breadcrumbs')): ?&gt;</code><br>
                                        <code>&nbsp;&nbsp;&lt;!-- breadcrumb code --&gt;</code><br>
                                        <code>&lt;?php endif; ?&gt;</code>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="<?= base_url('admin/themes') ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Themes
                                </a>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Auto-format JSON fields
    document.querySelectorAll('textarea[name*="settings"]').forEach(textarea => {
        if (textarea.id.includes('json') || textarea.value.trim().startsWith('{') || textarea.value.trim().startsWith('[')) {
            try {
                const parsed = JSON.parse(textarea.value);
                textarea.value = JSON.stringify(parsed, null, 2);
            } catch (e) {
                // Not valid JSON, leave as is
            }
        }
    });

    // Validate JSON on change
    document.querySelectorAll('textarea[name*="settings"]').forEach(textarea => {
        textarea.addEventListener('blur', function() {
            if (this.value.trim().startsWith('{') || this.value.trim().startsWith('[')) {
                try {
                    JSON.parse(this.value);
                    this.classList.remove('is-invalid');
                } catch (e) {
                    this.classList.add('is-invalid');
                }
            }
        });
    });

    // Auto-generate setting key from description
    document.getElementById('new_description').addEventListener('input', function() {
        const key = document.getElementById('new_key');
        if (!key.value) {
            const slug = this.value.toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '_')
                .replace(/^_+|_+$/g, '');
            key.value = slug;
        }
    });
</script>
<?= $this->endSection() ?>
