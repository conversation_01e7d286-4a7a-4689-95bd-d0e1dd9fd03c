<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Edit Template<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/themes') ?>">Themes</a>
<i class="fas fa-chevron-right"></i>
<span>Edit Template</span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" rel="stylesheet">
<style>
    .CodeMirror {
        border: 1px solid #ddd;
        border-radius: 4px;
        height: 400px;
    }
    .template-preview {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        background: #f8f9fa;
        min-height: 200px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Edit Template: <?= esc($template['name']) ?>
                </h5>
            </div>
            <div class="card-body">
                <form action="<?= base_url('admin/themes/update/' . $template['id']) ?>" method="post" enctype="multipart/form-data">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Basic Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Template Information</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label">Template Name *</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?= old('name', $template['name']) ?>" required>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="type" class="form-label">Template Type *</label>
                                            <select class="form-select" id="type" name="type" required>
                                                <option value="page" <?= old('type', $template['type']) === 'page' ? 'selected' : '' ?>>Page Template</option>
                                                <option value="blog" <?= old('type', $template['type']) === 'blog' ? 'selected' : '' ?>>Blog Template</option>
                                                <option value="layout" <?= old('type', $template['type']) === 'layout' ? 'selected' : '' ?>>Layout Template</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="file_path" class="form-label">File Path *</label>
                                        <input type="text" class="form-control" id="file_path" name="file_path" 
                                               value="<?= old('file_path', $template['file_path']) ?>" required>
                                        <div class="form-text">Path relative to app/Views/ directory</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"><?= old('description', $template['description']) ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?= old('status', $template['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= old('status', $template['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Template Content -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Template Content</h6>
                                </div>
                                <div class="card-body">
                                    <textarea id="template_content" name="template_content"><?= old('template_content', $template_content) ?></textarea>
                                    <div class="form-text mt-2">
                                        Edit your template code here. Changes will be saved to the file system.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <!-- Current Preview -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Preview Image</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($template['preview_image'])): ?>
                                    <div class="current-preview mb-3">
                                        <label class="form-label">Current Preview:</label>
                                        <div class="template-preview">
                                            <img src="<?= base_url($template['preview_image']) ?>" alt="Current Preview" class="img-fluid rounded">
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="preview_image" class="form-label">Upload New Preview</label>
                                        <input type="file" class="form-control" id="preview_image" name="preview_image" 
                                               accept="image/*" onchange="previewImage(this)">
                                        <div class="form-text">Upload a new screenshot or preview (optional)</div>
                                    </div>
                                    
                                    <div id="image_preview" class="template-preview text-center" style="display: none;">
                                        <img id="preview_img" src="" alt="New Preview" class="img-fluid rounded">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Template Info -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Template Info</h6>
                                </div>
                                <div class="card-body">
                                    <small class="text-muted">
                                        <strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($template['created_at'])) ?><br>
                                        <strong>Updated:</strong> <?= date('M j, Y g:i A', strtotime($template['updated_at'])) ?><br>
                                        <strong>Type:</strong> <?= ucfirst($template['type']) ?><br>
                                        <strong>Status:</strong> 
                                        <span class="badge bg-<?= $template['status'] === 'active' ? 'success' : 'secondary' ?>">
                                            <?= ucfirst($template['status']) ?>
                                        </span><br>
                                        <?php if ($template['is_default']): ?>
                                        <strong>Default:</strong> <span class="badge bg-primary">Yes</span>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                            
                            <!-- Template Variables -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Available Variables</h6>
                                </div>
                                <div class="card-body">
                                    <small class="text-muted">
                                        <strong>Common variables:</strong><br>
                                        <code>$title</code> - Page title<br>
                                        <code>$content</code> - Main content<br>
                                        <code>$meta_description</code> - Meta description<br>
                                        <code>$meta_keywords</code> - Meta keywords<br>
                                        <code>$user</code> - Current user data<br>
                                        <code>$settings</code> - Site settings<br>
                                        <br>
                                        <strong>Blog specific:</strong><br>
                                        <code>$post</code> - Blog post data<br>
                                        <code>$category</code> - Post category<br>
                                        <code>$tags</code> - Post tags<br>
                                        <code>$author</code> - Post author<br>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="<?= base_url('admin/themes') ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Themes
                                </a>
                                
                                <div>
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="previewTemplate()">
                                        <i class="fas fa-eye me-2"></i>Preview
                                    </button>
                                    <?php if (!$template['is_default']): ?>
                                    <button type="button" class="btn btn-outline-success me-2" onclick="setAsDefault(<?= $template['id'] ?>)">
                                        <i class="fas fa-star me-2"></i>Set as Default
                                    </button>
                                    <?php endif; ?>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Template
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/php/php.min.js"></script>

<script>
    // Initialize CodeMirror
    const editor = CodeMirror.fromTextArea(document.getElementById('template_content'), {
        lineNumbers: true,
        mode: 'application/x-httpd-php',
        theme: 'monokai',
        indentUnit: 4,
        lineWrapping: true,
        autoCloseTags: true,
        autoCloseBrackets: true
    });

    // Preview image function
    function previewImage(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('preview_img').src = e.target.result;
                document.getElementById('image_preview').style.display = 'block';
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    // Preview template function
    function previewTemplate() {
        alert('Template preview functionality would be implemented here');
    }

    // Set as default function
    function setAsDefault(templateId) {
        if (confirm('Are you sure you want to set this template as the default for its type?')) {
            fetch(`<?= base_url('admin/themes/set-default/') ?>${templateId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Failed to set template as default');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred');
            });
        }
    }
</script>
<?= $this->endSection() ?>
