<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>Template Analytics<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/themes') ?>">Themes</a></li>
<li class="breadcrumb-item active">Analytics</li>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .analytics-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 25px;
        margin-bottom: 30px;
        height: 100%;
    }
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        margin-bottom: 20px;
    }
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .metric-label {
        opacity: 0.9;
        font-size: 0.9rem;
    }
    .template-performance {
        border-left: 4px solid #667eea;
        padding-left: 15px;
        margin-bottom: 15px;
    }
    .performance-score {
        font-size: 1.5rem;
        font-weight: bold;
    }
    .score-excellent { color: #28a745; }
    .score-good { color: #ffc107; }
    .score-poor { color: #dc3545; }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Analytics Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="analytics-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4><i class="fas fa-chart-line me-3"></i>Template Analytics Dashboard</h4>
                    <p class="text-muted mb-0">Monitor template performance, usage statistics, and optimization insights.</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary active" data-period="7">7 Days</button>
                        <button type="button" class="btn btn-outline-primary" data-period="30">30 Days</button>
                        <button type="button" class="btn btn-outline-primary" data-period="90">90 Days</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="totalTemplates">12</div>
            <div class="metric-label">Total Templates</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="metric-value" id="activeTemplates">8</div>
            <div class="metric-label">Active Templates</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="metric-value" id="avgLoadTime">2.3s</div>
            <div class="metric-label">Avg Load Time</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
            <div class="metric-value" id="totalViews">15.2K</div>
            <div class="metric-label">Total Page Views</div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="analytics-card">
            <h6><i class="fas fa-chart-area me-2"></i>Template Usage Over Time</h6>
            <canvas id="usageChart" height="100"></canvas>
        </div>
    </div>
    <div class="col-md-4">
        <div class="analytics-card">
            <h6><i class="fas fa-chart-pie me-2"></i>Template Distribution</h6>
            <canvas id="distributionChart"></canvas>
        </div>
    </div>
</div>

<!-- Performance Analysis -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="analytics-card">
            <h6><i class="fas fa-tachometer-alt me-2"></i>Template Performance Scores</h6>
            
            <div class="template-performance">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>Villa Agency</strong>
                        <small class="text-muted d-block">TemplateMonster</small>
                    </div>
                    <div class="performance-score score-excellent">95</div>
                </div>
                <div class="progress mt-2" style="height: 6px;">
                    <div class="progress-bar bg-success" style="width: 95%"></div>
                </div>
            </div>
            
            <div class="template-performance">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>Klassy Cafe</strong>
                        <small class="text-muted d-block">TemplateMonster</small>
                    </div>
                    <div class="performance-score score-good">87</div>
                </div>
                <div class="progress mt-2" style="height: 6px;">
                    <div class="progress-bar bg-warning" style="width: 87%"></div>
                </div>
            </div>
            
            <div class="template-performance">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>Custom Blog</strong>
                        <small class="text-muted d-block">Custom Template</small>
                    </div>
                    <div class="performance-score score-good">78</div>
                </div>
                <div class="progress mt-2" style="height: 6px;">
                    <div class="progress-bar bg-warning" style="width: 78%"></div>
                </div>
            </div>
            
            <div class="template-performance">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>Snapshot Portfolio</strong>
                        <small class="text-muted d-block">TemplateMonster</small>
                    </div>
                    <div class="performance-score score-poor">65</div>
                </div>
                <div class="progress mt-2" style="height: 6px;">
                    <div class="progress-bar bg-danger" style="width: 65%"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="analytics-card">
            <h6><i class="fas fa-mobile-alt me-2"></i>Device Usage</h6>
            <canvas id="deviceChart"></canvas>
            
            <div class="mt-3">
                <div class="d-flex justify-content-between mb-2">
                    <span>Desktop</span>
                    <span class="fw-bold">45.2%</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Mobile</span>
                    <span class="fw-bold">38.7%</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>Tablet</span>
                    <span class="fw-bold">16.1%</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Optimization Recommendations -->
<div class="row mb-4">
    <div class="col-12">
        <div class="analytics-card">
            <h6><i class="fas fa-lightbulb me-2"></i>Optimization Recommendations</h6>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Image Optimization</h6>
                        <p class="mb-2">3 templates have unoptimized images that could be compressed.</p>
                        <button class="btn btn-warning btn-sm">Optimize Images</button>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-code me-2"></i>CSS Minification</h6>
                        <p class="mb-2">Enable CSS minification to reduce load times by up to 25%.</p>
                        <button class="btn btn-info btn-sm">Enable Minification</button>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-sync me-2"></i>Template Updates</h6>
                        <p class="mb-2">2 TemplateMonster templates have updates available.</p>
                        <button class="btn btn-success btn-sm">Update Templates</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Popular Templates -->
<div class="row">
    <div class="col-12">
        <div class="analytics-card">
            <h6><i class="fas fa-star me-2"></i>Most Popular Templates</h6>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Template</th>
                            <th>Source</th>
                            <th>Page Views</th>
                            <th>Avg. Load Time</th>
                            <th>Performance</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="https://templatemo.com/templates/tm_591_villa_agency/preview.jpg" 
                                         class="rounded me-3" width="40" height="30" style="object-fit: cover;">
                                    <div>
                                        <strong>Villa Agency</strong>
                                        <small class="text-muted d-block">Real Estate</small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-primary">TemplateMonster</span></td>
                            <td>5,234</td>
                            <td>2.1s</td>
                            <td><span class="badge bg-success">95</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Analyze</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="https://templatemo.com/templates/tm_558_klassy_cafe/preview.jpg" 
                                         class="rounded me-3" width="40" height="30" style="object-fit: cover;">
                                    <div>
                                        <strong>Klassy Cafe</strong>
                                        <small class="text-muted d-block">Restaurant</small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-primary">TemplateMonster</span></td>
                            <td>3,891</td>
                            <td>2.4s</td>
                            <td><span class="badge bg-warning">87</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Analyze</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary rounded me-3 d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 30px;">
                                        <i class="fas fa-file-alt text-white"></i>
                                    </div>
                                    <div>
                                        <strong>Custom Blog</strong>
                                        <small class="text-muted d-block">Blog</small>
                                    </div>
                                </div>
                            </td>
                            <td><span class="badge bg-secondary">Custom</span></td>
                            <td>2,567</td>
                            <td>3.1s</td>
                            <td><span class="badge bg-warning">78</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Analyze</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Usage Chart
    const usageCtx = document.getElementById('usageChart').getContext('2d');
    new Chart(usageCtx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Villa Agency',
                data: [120, 190, 300, 500, 200, 300, 450],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4
            }, {
                label: 'Klassy Cafe',
                data: [80, 150, 200, 300, 150, 250, 300],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Distribution Chart
    const distributionCtx = document.getElementById('distributionChart').getContext('2d');
    new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['TemplateMonster', 'Custom', 'Third Party'],
            datasets: [{
                data: [8, 3, 1],
                backgroundColor: ['#667eea', '#28a745', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Device Chart
    const deviceCtx = document.getElementById('deviceChart').getContext('2d');
    new Chart(deviceCtx, {
        type: 'bar',
        data: {
            labels: ['Desktop', 'Mobile', 'Tablet'],
            datasets: [{
                data: [45.2, 38.7, 16.1],
                backgroundColor: ['#667eea', '#28a745', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 50
                }
            }
        }
    });

    // Period selection
    document.querySelectorAll('[data-period]').forEach(button => {
        button.addEventListener('click', function() {
            document.querySelectorAll('[data-period]').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Update charts with new data (simulation)
            updateAnalytics(this.dataset.period);
        });
    });

    function updateAnalytics(period) {
        // Simulate data update
        console.log('Updating analytics for period:', period);
        
        // Update metrics
        const metrics = {
            7: { templates: 12, active: 8, loadTime: '2.3s', views: '15.2K' },
            30: { templates: 12, active: 9, loadTime: '2.1s', views: '45.8K' },
            90: { templates: 12, active: 10, loadTime: '2.0s', views: '128.5K' }
        };
        
        const data = metrics[period];
        document.getElementById('totalTemplates').textContent = data.templates;
        document.getElementById('activeTemplates').textContent = data.active;
        document.getElementById('avgLoadTime').textContent = data.loadTime;
        document.getElementById('totalViews').textContent = data.views;
    }
</script>
<?= $this->endSection() ?>
