<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Bulk Template Manager<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/themes') ?>">Themes</a>
<i class="fas fa-chevron-right"></i>
<span>Bulk Manager</span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<style>
    .bulk-actions {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 20px;
        margin-bottom: 30px;
    }
    .template-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }
    .template-item {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
        transition: transform 0.3s ease;
        position: relative;
    }
    .template-item:hover {
        transform: translateY(-5px);
    }
    .template-item.selected {
        border: 3px solid #667eea;
        transform: translateY(-5px);
    }
    .template-checkbox {
        position: absolute;
        top: 15px;
        left: 15px;
        z-index: 10;
        transform: scale(1.2);
    }
    .template-thumbnail {
        height: 150px;
        object-fit: cover;
        width: 100%;
    }
    .template-status {
        position: absolute;
        top: 15px;
        right: 15px;
        z-index: 10;
    }
    .bulk-progress {
        display: none;
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 30px;
        text-align: center;
        margin-bottom: 30px;
    }
    .action-buttons {
        display: none;
        gap: 10px;
    }
    .action-buttons.show {
        display: flex;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Bulk Actions Panel -->
<div class="bulk-actions">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h5 class="mb-0">
                <i class="fas fa-tasks me-2"></i>Bulk Template Manager
            </h5>
            <small class="text-muted">Select multiple templates to perform bulk operations</small>
        </div>
        <div class="col-md-6">
            <div class="d-flex justify-content-end align-items-center gap-2">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectAll()">
                    <i class="fas fa-check-square me-1"></i>Select All
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectNone()">
                    <i class="fas fa-square me-1"></i>Select None
                </button>
                <span class="badge bg-primary" id="selectedCount">0 selected</span>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="action-buttons mt-3" id="actionButtons">
        <button type="button" class="btn btn-success" onclick="bulkInstall()">
            <i class="fas fa-download me-1"></i>Install Selected
        </button>
        <button type="button" class="btn btn-warning" onclick="bulkUpdate()">
            <i class="fas fa-sync me-1"></i>Update Selected
        </button>
        <button type="button" class="btn btn-info" onclick="bulkExport()">
            <i class="fas fa-file-export me-1"></i>Export Settings
        </button>
        <button type="button" class="btn btn-danger" onclick="bulkDelete()">
            <i class="fas fa-trash me-1"></i>Remove Selected
        </button>
    </div>
</div>

<!-- Bulk Progress -->
<div class="bulk-progress" id="bulkProgress">
    <div class="mb-3">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Processing...</span>
        </div>
    </div>
    <h5 id="progressTitle">Processing Templates...</h5>
    <div class="progress mb-3">
        <div class="progress-bar progress-bar-striped progress-bar-animated" 
             role="progressbar" style="width: 0%" id="progressBar">
        </div>
    </div>
    <div id="progressStatus" class="text-muted">
        <small>Preparing operation...</small>
    </div>
</div>

<!-- Template Categories -->
<div class="row mb-4">
    <div class="col-12">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary active" data-category="all">
                All Templates
            </button>
            <button type="button" class="btn btn-outline-primary" data-category="installed">
                Installed
            </button>
            <button type="button" class="btn btn-outline-primary" data-category="available">
                Available
            </button>
            <button type="button" class="btn btn-outline-primary" data-category="Business">
                Business
            </button>
            <button type="button" class="btn btn-outline-primary" data-category="Restaurant">
                Restaurant
            </button>
            <button type="button" class="btn btn-outline-primary" data-category="Portfolio">
                Portfolio
            </button>
        </div>
    </div>
</div>

<!-- Templates Grid -->
<div class="template-grid" id="templateGrid">
    <?php foreach ($templates as $template): ?>
    <div class="template-item" data-id="<?= $template['id'] ?>" data-category="<?= $template['category'] ?>">
        <input type="checkbox" class="form-check-input template-checkbox" 
               value="<?= $template['id'] ?>" onchange="updateSelection()">
        
        <div class="template-status">
            <?php if ($template['installed']): ?>
            <span class="badge bg-success">Installed</span>
            <?php else: ?>
            <span class="badge bg-secondary">Available</span>
            <?php endif; ?>
        </div>
        
        <img src="<?= $template['thumbnail'] ?>" class="template-thumbnail" alt="<?= esc($template['name']) ?>">
        
        <div class="p-3">
            <h6 class="mb-2"><?= esc($template['name']) ?></h6>
            <p class="text-muted small mb-2"><?= esc($template['description']) ?></p>
            
            <div class="d-flex justify-content-between align-items-center">
                <div class="template-rating text-warning">
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                        <i class="fas fa-star<?= $i <= floor($template['rating']) ? '' : '-o' ?>"></i>
                    <?php endfor; ?>
                    <small class="text-muted ms-1"><?= $template['rating'] ?></small>
                </div>
                <small class="text-muted">
                    <i class="fas fa-download me-1"></i><?= $template['downloads'] ?>
                </small>
            </div>
            
            <div class="mt-2">
                <?php foreach (array_slice($template['features'], 0, 2) as $feature): ?>
                <span class="badge bg-light text-dark me-1"><?= esc($feature) ?></span>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- Bulk Operation Modals -->
<div class="modal fade" id="bulkConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkModalTitle">Confirm Bulk Operation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="bulkModalMessage">Are you sure you want to perform this operation on the selected templates?</p>
                <div id="selectedTemplatesList" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmBulkAction">Confirm</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let selectedTemplates = [];
    let currentOperation = null;

    // Update selection
    function updateSelection() {
        selectedTemplates = [];
        document.querySelectorAll('.template-checkbox:checked').forEach(checkbox => {
            selectedTemplates.push(checkbox.value);
            checkbox.closest('.template-item').classList.add('selected');
        });
        
        document.querySelectorAll('.template-checkbox:not(:checked)').forEach(checkbox => {
            checkbox.closest('.template-item').classList.remove('selected');
        });
        
        const count = selectedTemplates.length;
        document.getElementById('selectedCount').textContent = count + ' selected';
        
        const actionButtons = document.getElementById('actionButtons');
        if (count > 0) {
            actionButtons.classList.add('show');
        } else {
            actionButtons.classList.remove('show');
        }
    }

    // Select all templates
    function selectAll() {
        document.querySelectorAll('.template-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        updateSelection();
    }

    // Select none
    function selectNone() {
        document.querySelectorAll('.template-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        updateSelection();
    }

    // Category filtering
    document.querySelectorAll('[data-category]').forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // Update active button
            document.querySelectorAll('[data-category]').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter templates
            document.querySelectorAll('.template-item').forEach(item => {
                const itemCategory = item.dataset.category;
                const isInstalled = item.querySelector('.badge').textContent === 'Installed';
                
                let show = false;
                if (category === 'all') {
                    show = true;
                } else if (category === 'installed') {
                    show = isInstalled;
                } else if (category === 'available') {
                    show = !isInstalled;
                } else {
                    show = itemCategory === category;
                }
                
                item.style.display = show ? 'block' : 'none';
            });
        });
    });

    // Bulk operations
    function bulkInstall() {
        const availableTemplates = selectedTemplates.filter(id => {
            const item = document.querySelector(`[data-id="${id}"]`);
            return item.querySelector('.badge').textContent === 'Available';
        });
        
        if (availableTemplates.length === 0) {
            alert('No available templates selected for installation.');
            return;
        }
        
        showBulkConfirm('Install Templates', 
            `Install ${availableTemplates.length} selected template(s)?`, 
            availableTemplates, 'install');
    }

    function bulkUpdate() {
        const installedTemplates = selectedTemplates.filter(id => {
            const item = document.querySelector(`[data-id="${id}"]`);
            return item.querySelector('.badge').textContent === 'Installed';
        });
        
        if (installedTemplates.length === 0) {
            alert('No installed templates selected for update.');
            return;
        }
        
        showBulkConfirm('Update Templates', 
            `Update ${installedTemplates.length} selected template(s)?`, 
            installedTemplates, 'update');
    }

    function bulkExport() {
        if (selectedTemplates.length === 0) {
            alert('Please select templates to export.');
            return;
        }
        
        showBulkConfirm('Export Template Settings', 
            `Export customization settings for ${selectedTemplates.length} selected template(s)?`, 
            selectedTemplates, 'export');
    }

    function bulkDelete() {
        const installedTemplates = selectedTemplates.filter(id => {
            const item = document.querySelector(`[data-id="${id}"]`);
            return item.querySelector('.badge').textContent === 'Installed';
        });
        
        if (installedTemplates.length === 0) {
            alert('No installed templates selected for removal.');
            return;
        }
        
        showBulkConfirm('Remove Templates', 
            `Remove ${installedTemplates.length} selected template(s)? This action cannot be undone.`, 
            installedTemplates, 'delete');
    }

    function showBulkConfirm(title, message, templates, operation) {
        document.getElementById('bulkModalTitle').textContent = title;
        document.getElementById('bulkModalMessage').textContent = message;
        
        // Show selected templates list
        const templatesList = document.getElementById('selectedTemplatesList');
        templatesList.innerHTML = '<strong>Selected Templates:</strong><ul class="mt-2">';
        templates.forEach(id => {
            const item = document.querySelector(`[data-id="${id}"]`);
            const name = item.querySelector('h6').textContent;
            templatesList.innerHTML += `<li>${name}</li>`;
        });
        templatesList.innerHTML += '</ul>';
        
        currentOperation = { operation, templates };
        
        const modal = new bootstrap.Modal(document.getElementById('bulkConfirmModal'));
        modal.show();
    }

    // Confirm bulk action
    document.getElementById('confirmBulkAction').addEventListener('click', function() {
        if (!currentOperation) return;
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('bulkConfirmModal'));
        modal.hide();
        
        executeBulkOperation(currentOperation.operation, currentOperation.templates);
    });

    function executeBulkOperation(operation, templates) {
        // Show progress
        document.getElementById('bulkProgress').style.display = 'block';
        document.getElementById('progressTitle').textContent = `${operation.charAt(0).toUpperCase() + operation.slice(1)}ing Templates...`;
        
        let completed = 0;
        const total = templates.length;
        
        // Process templates one by one
        processTemplate(0);
        
        function processTemplate(index) {
            if (index >= templates.length) {
                // All done
                document.getElementById('bulkProgress').style.display = 'none';
                alert(`Bulk ${operation} completed successfully!`);
                location.reload();
                return;
            }
            
            const templateId = templates[index];
            const progress = ((index + 1) / total) * 100;
            
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('progressStatus').innerHTML = 
                `<small>Processing template ${index + 1} of ${total}...</small>`;
            
            // Simulate API call (replace with actual implementation)
            setTimeout(() => {
                processTemplate(index + 1);
            }, 1000);
        }
    }
</script>
<?= $this->endSection() ?>
