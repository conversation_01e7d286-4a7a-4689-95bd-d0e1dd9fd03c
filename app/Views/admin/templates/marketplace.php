<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Template Marketplace<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/themes') ?>">Themes</a>
<i class="fas fa-chevron-right"></i>
<span>Template Marketplace</span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<style>
    .template-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        height: 100%;
    }
    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }
    .template-thumbnail {
        height: 200px;
        object-fit: cover;
        width: 100%;
    }
    .template-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 2;
    }
    .template-rating {
        color: #ffc107;
    }
    .template-features {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 10px;
    }
    .feature-tag {
        background: #e9ecef;
        color: #495057;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
    }
    .marketplace-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
    }
    .category-filter {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .installed-badge {
        background: #28a745;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 2;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">
            <i class="fas fa-store me-3"></i>Template Marketplace
        </h1>
        <p class="page-subtitle">Discover and install professional templates from TemplateMonster. All templates are free and ready to use with your CMS.</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/templates/bulk-manager') ?>" class="btn btn-admin-secondary">
            <i class="fas fa-layer-group me-2"></i>Bulk Manager
        </a>
        <a href="<?= base_url('admin/templates/analytics') ?>" class="btn btn-admin-primary">
            <i class="fas fa-chart-line me-2"></i>Analytics
        </a>
    </div>
</div>

<!-- Category Filter -->
<div class="admin-card mb-4">
    <div class="admin-card-body">
        <div class="row align-items-center">
            <div class="col-md-3">
                <h6 class="mb-0">Filter by Category:</h6>
            </div>
            <div class="col-md-9">
                <div class="btn-group flex-wrap" role="group">
                    <button type="button" class="btn btn-admin-primary active" data-category="all">All</button>
                    <button type="button" class="btn btn-admin-secondary" data-category="Business">Business</button>
                    <button type="button" class="btn btn-admin-secondary" data-category="Restaurant">Restaurant</button>
                    <button type="button" class="btn btn-admin-secondary" data-category="Portfolio">Portfolio</button>
                    <button type="button" class="btn btn-admin-secondary" data-category="Technology">Technology</button>
                    <button type="button" class="btn btn-admin-secondary" data-category="Gaming">Gaming</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Templates Grid -->
<div class="row" id="templatesGrid">
    <?php foreach ($templates as $template): ?>
    <div class="col-lg-4 col-md-6 mb-4 template-item" data-category="<?= $template['category'] ?>">
        <div class="card template-card">
            <div class="position-relative">
                <img src="<?= $template['thumbnail'] ?>" class="template-thumbnail" alt="<?= esc($template['name']) ?>">
                
                <!-- Installed Badge -->
                <?php 
                $isInstalled = false;
                foreach ($installed_templates as $installed) {
                    if (isset($installed['template_id']) && $installed['template_id'] === $template['id']) {
                        $isInstalled = true;
                        break;
                    }
                }
                ?>
                <?php if ($isInstalled): ?>
                <span class="installed-badge">
                    <i class="fas fa-check me-1"></i>Installed
                </span>
                <?php endif; ?>
                
                <!-- Category Badge -->
                <span class="template-badge">
                    <span class="badge bg-primary"><?= $template['category'] ?></span>
                </span>
            </div>
            
            <div class="card-body">
                <h5 class="card-title"><?= esc($template['name']) ?></h5>
                <p class="card-text text-muted small"><?= esc($template['description']) ?></p>
                
                <!-- Rating and Downloads -->
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="template-rating">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star<?= $i <= floor($template['rating']) ? '' : '-o' ?>"></i>
                        <?php endfor; ?>
                        <span class="ms-1 text-muted small"><?= $template['rating'] ?></span>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-download me-1"></i><?= $template['downloads'] ?>
                    </small>
                </div>
                
                <!-- Features -->
                <div class="template-features">
                    <?php foreach (array_slice($template['features'], 0, 3) as $feature): ?>
                    <span class="feature-tag"><?= esc($feature) ?></span>
                    <?php endforeach; ?>
                    <?php if (count($template['features']) > 3): ?>
                    <span class="feature-tag">+<?= count($template['features']) - 3 ?> more</span>
                    <?php endif; ?>
                </div>
                
                <!-- Pages -->
                <div class="mt-2">
                    <small class="text-muted">
                        <strong>Pages:</strong> <?= implode(', ', array_slice($template['pages'], 0, 3)) ?>
                        <?php if (count($template['pages']) > 3): ?>
                        <span>+<?= count($template['pages']) - 3 ?> more</span>
                        <?php endif; ?>
                    </small>
                </div>
            </div>
            
            <div class="card-footer bg-transparent">
                <div class="d-flex gap-2">
                    <a href="<?= $template['preview_url'] ?>" target="_blank" class="btn btn-outline-primary btn-sm flex-fill">
                        <i class="fas fa-eye me-1"></i>Preview
                    </a>
                    
                    <?php if (!$isInstalled): ?>
                    <button type="button" class="btn btn-primary btn-sm flex-fill" 
                            onclick="installTemplate('<?= $template['id'] ?>', '<?= esc($template['name']) ?>')">
                        <i class="fas fa-download me-1"></i>Install
                    </button>
                    <?php else: ?>
                    <button type="button" class="btn btn-success btn-sm flex-fill" disabled>
                        <i class="fas fa-check me-1"></i>Installed
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- No Results Message -->
<div id="noResults" class="admin-empty-state" style="display: none;">
    <i class="fas fa-search"></i>
    <h4>No templates found</h4>
    <p>Try selecting a different category or check back later for new templates.</p>
</div>

<!-- Installation Modal -->
<div class="modal fade" id="installModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Install Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Installing...</span>
                    </div>
                    <h6 id="installTemplateName">Installing template...</h6>
                    <p class="text-muted">Please wait while we download and install the template files.</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Category filtering
    document.querySelectorAll('[data-category]').forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // Update active button
            document.querySelectorAll('[data-category]').forEach(btn => {
                btn.classList.remove('active');
                btn.classList.remove('btn-admin-primary');
                btn.classList.add('btn-admin-secondary');
            });
            this.classList.add('active');
            this.classList.remove('btn-admin-secondary');
            this.classList.add('btn-admin-primary');
            
            // Filter templates
            const templates = document.querySelectorAll('.template-item');
            let visibleCount = 0;
            
            templates.forEach(template => {
                if (category === 'all' || template.dataset.category === category) {
                    template.style.display = 'block';
                    visibleCount++;
                } else {
                    template.style.display = 'none';
                }
            });
            
            // Show/hide no results message
            document.getElementById('noResults').style.display = visibleCount === 0 ? 'block' : 'none';
        });
    });

    // Install template function
    function installTemplate(templateId, templateName) {
        // Show installation modal
        document.getElementById('installTemplateName').textContent = `Installing ${templateName}...`;
        const installModal = new bootstrap.Modal(document.getElementById('installModal'));
        installModal.show();
        
        // Send installation request
        adminAPI(`<?= base_url('admin/templates/marketplace/install/') ?>${templateId}`, {
            method: 'POST'
        })
        .then(data => {
            installModal.hide();

            if (data.success) {
                showAlert(data.message || 'Template installed successfully!', 'success');

                // Update the install button
                const button = document.querySelector(`[onclick="installTemplate('${templateId}', '${templateName}')"]`);
                if (button) {
                    button.outerHTML = `
                        <button type="button" class="btn btn-success btn-sm flex-fill" disabled>
                            <i class="fas fa-check me-1"></i>Installed
                        </button>
                    `;

                    // Add installed badge
                    const card = button.closest('.template-card');
                    const thumbnail = card.querySelector('.template-thumbnail');
                    const badge = document.createElement('span');
                    badge.className = 'installed-badge';
                    badge.innerHTML = '<i class="fas fa-check me-1"></i>Installed';
                    thumbnail.parentElement.appendChild(badge);
                }
            } else {
                showAlert(data.message || 'Installation failed', 'error');
            }
        })
        .catch(error => {
            installModal.hide();
            console.error('Installation error:', error);
            showAlert('Installation failed. Please try again.', 'error');
        });
    }

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        document.querySelectorAll('.alert').forEach(alert => {
            if (alert.classList.contains('show')) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 150);
            }
        });
    }, 5000);
</script>
<?= $this->endSection() ?>
