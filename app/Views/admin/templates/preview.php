<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Preview: <?= esc($template['name']) ?><?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/templates/marketplace') ?>">Template Store</a>
<i class="fas fa-chevron-right"></i>
<span>Preview: <?= esc($template['name']) ?></span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<style>
    .preview-container {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
    }
    .preview-frame {
        width: 100%;
        height: 600px;
        border: 1px solid #ddd;
        border-radius: 10px;
        background: white;
    }
    .template-info-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 25px;
        margin-bottom: 20px;
    }
    .feature-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        margin: 2px;
        display: inline-block;
    }
    .rating-stars {
        color: #ffc107;
        font-size: 1.2rem;
    }
    .preview-actions {
        position: sticky;
        top: 20px;
        z-index: 100;
    }
    .device-preview {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }
    .device-btn {
        padding: 8px 15px;
        border: 2px solid #dee2e6;
        background: white;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .device-btn.active {
        border-color: #667eea;
        background: #667eea;
        color: white;
    }
    .device-btn:hover {
        border-color: #667eea;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-lg-8">
        <!-- Preview Container -->
        <div class="preview-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5><i class="fas fa-eye me-2"></i>Live Preview</h5>
                
                <!-- Device Preview Options -->
                <div class="device-preview">
                    <button class="device-btn active" onclick="setPreviewSize('100%', '600px')" data-device="desktop">
                        <i class="fas fa-desktop me-1"></i>Desktop
                    </button>
                    <button class="device-btn" onclick="setPreviewSize('768px', '600px')" data-device="tablet">
                        <i class="fas fa-tablet-alt me-1"></i>Tablet
                    </button>
                    <button class="device-btn" onclick="setPreviewSize('375px', '600px')" data-device="mobile">
                        <i class="fas fa-mobile-alt me-1"></i>Mobile
                    </button>
                </div>
            </div>
            
            <div class="text-center">
                <iframe id="previewFrame" src="<?= $template['preview_url'] ?>" 
                        class="preview-frame" 
                        style="width: 100%; height: 600px;">
                </iframe>
            </div>
            
            <div class="mt-3 text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    This is a live preview from TemplateMonster. The actual template will be customized for your CMS.
                </small>
            </div>
        </div>
        
        <!-- Template Pages -->
        <div class="template-info-card">
            <h6><i class="fas fa-sitemap me-2"></i>Template Pages</h6>
            <div class="row">
                <?php foreach ($template['pages'] as $page): ?>
                <div class="col-md-4 mb-2">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-file-alt text-primary me-2"></i>
                        <span><?= esc($page) ?></span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Template Features -->
        <div class="template-info-card">
            <h6><i class="fas fa-star me-2"></i>Template Features</h6>
            <div class="mb-3">
                <?php foreach ($template['features'] as $feature): ?>
                <span class="feature-badge"><?= esc($feature) ?></span>
                <?php endforeach; ?>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-muted">What's Included:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Responsive HTML/CSS</li>
                        <li><i class="fas fa-check text-success me-2"></i>Bootstrap Framework</li>
                        <li><i class="fas fa-check text-success me-2"></i>JavaScript Components</li>
                        <li><i class="fas fa-check text-success me-2"></i>Font Awesome Icons</li>
                        <li><i class="fas fa-check text-success me-2"></i>Google Fonts</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-muted">CMS Integration:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>CodeIgniter 4 Views</li>
                        <li><i class="fas fa-check text-success me-2"></i>Dynamic Content Areas</li>
                        <li><i class="fas fa-check text-success me-2"></i>Admin Panel Integration</li>
                        <li><i class="fas fa-check text-success me-2"></i>SEO Optimization</li>
                        <li><i class="fas fa-check text-success me-2"></i>User Authentication</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Template Info -->
        <div class="preview-actions">
            <div class="template-info-card">
                <div class="text-center mb-3">
                    <h4><?= esc($template['name']) ?></h4>
                    <p class="text-muted"><?= esc($template['description']) ?></p>
                    
                    <!-- Rating -->
                    <div class="rating-stars mb-2">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star<?= $i <= floor($template['rating']) ? '' : '-o' ?>"></i>
                        <?php endfor; ?>
                        <span class="ms-2 text-muted"><?= $template['rating'] ?>/5</span>
                    </div>
                    
                    <!-- Downloads -->
                    <div class="mb-3">
                        <span class="badge bg-info">
                            <i class="fas fa-download me-1"></i><?= $template['downloads'] ?> downloads
                        </span>
                        <span class="badge bg-secondary ms-1"><?= $template['category'] ?></span>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary btn-lg" onclick="installTemplate()">
                        <i class="fas fa-download me-2"></i>Install Template
                    </button>
                    
                    <a href="<?= $template['preview_url'] ?>" target="_blank" class="btn btn-outline-primary">
                        <i class="fas fa-external-link-alt me-2"></i>View Full Preview
                    </a>

                    <button type="button" class="btn btn-outline-info" onclick="customizeTemplate()">
                        <i class="fas fa-palette me-2"></i>Customize Template
                    </button>
                    
                    <a href="<?= base_url('admin/templates/marketplace') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Store
                    </a>
                </div>
            </div>
            
            <!-- Template Stats -->
            <div class="template-info-card">
                <h6><i class="fas fa-chart-bar me-2"></i>Template Statistics</h6>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-primary mb-0"><?= count($template['pages']) ?></h5>
                            <small class="text-muted">Pages</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-success mb-0"><?= count($template['features']) ?></h5>
                            <small class="text-muted">Features</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-warning mb-0"><?= $template['rating'] ?></h5>
                        <small class="text-muted">Rating</small>
                    </div>
                </div>
            </div>
            
            <!-- Installation Requirements -->
            <div class="template-info-card">
                <h6><i class="fas fa-server me-2"></i>Requirements</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success me-2"></i>CodeIgniter 4.0+</li>
                    <li><i class="fas fa-check text-success me-2"></i>PHP 7.4+</li>
                    <li><i class="fas fa-check text-success me-2"></i>MySQL 5.7+</li>
                    <li><i class="fas fa-check text-success me-2"></i>Bootstrap 5</li>
                    <li><i class="fas fa-check text-success me-2"></i>Modern Browser</li>
                </ul>
            </div>
            
            <!-- Support Info -->
            <div class="template-info-card">
                <h6><i class="fas fa-life-ring me-2"></i>Support & Updates</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success me-2"></i>Free Template</li>
                    <li><i class="fas fa-check text-success me-2"></i>Regular Updates</li>
                    <li><i class="fas fa-check text-success me-2"></i>Documentation</li>
                    <li><i class="fas fa-check text-success me-2"></i>Community Support</li>
                </ul>
                
                <div class="mt-3">
                    <a href="https://templatemo.com" target="_blank" class="btn btn-outline-info btn-sm w-100">
                        <i class="fas fa-external-link-alt me-1"></i>Visit TemplateMonster
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Installation Modal -->
<div class="modal fade" id="installModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Installing <?= esc($template['name']) ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="mb-4">
                        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                            <span class="visually-hidden">Installing...</span>
                        </div>
                    </div>
                    
                    <h5>Installing Template...</h5>
                    <p class="text-muted">Please wait while we download and install the template files.</p>
                    
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="installProgress">
                        </div>
                    </div>
                    
                    <div id="installStatus" class="text-muted">
                        <small>Preparing installation...</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Device preview functionality
    function setPreviewSize(width, height) {
        const frame = document.getElementById('previewFrame');
        const container = frame.parentElement;
        
        // Update frame size
        frame.style.width = width;
        frame.style.height = height;
        
        // Center frame if smaller than container
        if (width !== '100%') {
            container.style.display = 'flex';
            container.style.justifyContent = 'center';
        } else {
            container.style.display = 'block';
        }
        
        // Update active button
        document.querySelectorAll('.device-btn').forEach(btn => btn.classList.remove('active'));
        event.target.closest('.device-btn').classList.add('active');
    }

    // Install template function
    function installTemplate() {
        const modal = new bootstrap.Modal(document.getElementById('installModal'));
        modal.show();
        
        // Simulate installation progress
        let progress = 0;
        const progressBar = document.getElementById('installProgress');
        const statusText = document.getElementById('installStatus');
        
        const steps = [
            'Downloading template files...',
            'Extracting assets...',
            'Converting to CodeIgniter format...',
            'Installing CSS and JavaScript...',
            'Creating database entries...',
            'Finalizing installation...'
        ];
        
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 100) progress = 100;
            
            progressBar.style.width = progress + '%';
            
            const stepIndex = Math.floor((progress / 100) * steps.length);
            if (stepIndex < steps.length) {
                statusText.innerHTML = `<small>${steps[stepIndex]}</small>`;
            }
            
            if (progress >= 100) {
                clearInterval(interval);
                
                // Send actual installation request
                fetch('<?= base_url('admin/templates/marketplace/install/' . $template['id']) ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    modal.hide();
                    
                    if (data.success) {
                        // Show success message
                        const alert = document.createElement('div');
                        alert.className = 'alert alert-success alert-dismissible fade show';
                        alert.innerHTML = `
                            <i class="fas fa-check-circle me-2"></i>
                            Template installed successfully! You can now use it in your themes.
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;
                        document.querySelector('.content-area').insertBefore(alert, document.querySelector('.row'));
                        
                        // Update install button
                        const installBtn = document.querySelector('[onclick="installTemplate()"]');
                        installBtn.innerHTML = '<i class="fas fa-check me-2"></i>Installed';
                        installBtn.className = 'btn btn-success btn-lg';
                        installBtn.disabled = true;
                        
                    } else {
                        alert('Installation failed: ' + data.message);
                    }
                })
                .catch(error => {
                    modal.hide();
                    console.error('Installation error:', error);
                    alert('Installation failed. Please try again.');
                });
            }
        }, 200);
    }

    // Customize template function
    function customizeTemplate() {
        // Check if template is installed first
        const installBtn = document.querySelector('[onclick="installTemplate()"]');
        if (installBtn && !installBtn.disabled) {
            alert('Please install the template first before customizing it.');
            return;
        }

        // Redirect to customizer (this would need the template ID from database)
        alert('Template customization will be available after installation. Install the template first.');
    }
</script>
<?= $this->endSection() ?>
