<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Menu Builder<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/menus') ?>">Navigation Menus</a>
<i class="fas fa-chevron-right"></i>
<span>Menu Builder</span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<!-- Sortable.js for drag and drop -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<style>
/* Menu Builder Styles */
.menu-builder {
    display: flex;
    gap: 20px;
    height: calc(100vh - 200px);
}

.menu-items-panel {
    flex: 1;
}

.menu-structure-panel {
    flex: 2;
}

.menu-item {
    background: var(--admin-light);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    cursor: move;
    transition: all 0.3s ease;
}

.menu-item:hover {
    background: #e9ecef;
    border-color: var(--admin-primary);
    transform: translateY(-1px);
    box-shadow: var(--admin-shadow-lg);
}

.menu-structure {
    min-height: 400px;
    border: 2px dashed var(--admin-border);
    border-radius: 12px;
    padding: 24px;
    background: var(--admin-light);
}

.menu-structure.drag-over {
    border-color: var(--admin-primary);
    background: rgba(var(--admin-primary-rgb), 0.05);
}

.menu-structure-item {
    background: white;
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    position: relative;
    cursor: move;
    transition: all 0.3s ease;
}

.menu-structure-item:hover {
    border-color: var(--admin-primary);
    box-shadow: var(--admin-shadow);
}

.menu-structure-item .item-controls {
    position: absolute;
    top: 12px;
    right: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.menu-structure-item:hover .item-controls {
    opacity: 1;
}

.menu-structure-item .item-controls .btn {
    padding: 4px 8px;
    font-size: 12px;
    margin-left: 4px;
}

.nested-items {
    margin-left: 30px;
    margin-top: 12px;
    border-left: 2px solid var(--admin-border);
    padding-left: 16px;
}

.item-form {
    background: var(--admin-light);
    border: 1px solid var(--admin-border);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.empty-menu {
    text-align: center;
    color: #6c757d;
    padding: 60px 20px;
}

.empty-menu i {
    color: var(--admin-primary);
    opacity: 0.5;
}

.item-type-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Menu Builder</h1>
        <p class="page-subtitle">
            <i class="fas fa-info-circle me-1"></i>
            Drag and drop to organize your menu items
        </p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/menus') ?>" class="btn btn-admin-secondary me-2">
            <i class="fas fa-arrow-left me-2"></i>Back to Menus
        </a>
        <button type="button" class="btn btn-admin-primary" onclick="saveMenuStructure()">
            <i class="fas fa-save me-2"></i>Save Menu
        </button>
    </div>
</div>
<!-- Menu Builder Interface -->
<div class="menu-builder">
    <!-- Available Items Panel -->
    <div class="menu-items-panel">
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>Add Menu Items
                </h5>
            </div>
            <div class="admin-card-body">
                <!-- Add Custom Link -->
                <div class="item-form">
                    <h6>Custom Link</h6>
                    <form id="customLinkForm">
                        <div class="mb-3">
                            <input type="text" class="form-control"
                                   id="customTitle" placeholder="Link Text" required>
                        </div>
                        <div class="mb-3">
                            <input type="url" class="form-control"
                                   id="customUrl" placeholder="https://example.com" required>
                        </div>
                        <button type="submit" class="btn btn-admin-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Add Link
                        </button>
                    </form>
                </div>

                <!-- Available Pages -->
                <div class="item-form">
                    <h6>Pages</h6>
                    <div class="available-pages" style="max-height: 300px; overflow-y: auto;">
                        <?php foreach ($availablePages as $page): ?>
                        <div class="menu-item" data-type="page" data-page-id="<?= $page['id'] ?>"
                             data-title="<?= esc($page['title']) ?>" data-url="/<?= esc($page['slug']) ?>">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?= esc($page['title']) ?></strong>
                                    <br>
                                    <small class="text-muted">/<?= esc($page['slug']) ?></small>
                                </div>
                                <button type="button" class="btn btn-sm btn-admin-primary"
                                        onclick="addPageToMenu(<?= $page['id'] ?>, '<?= esc($page['title']) ?>', '/<?= esc($page['slug']) ?>')">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <?php if (empty($availablePages)): ?>
                        <div class="admin-empty-state">
                            <i class="fas fa-info-circle"></i>
                            <h6>No published pages available</h6>
                            <p>Create some pages first to add them to your menu</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
                        
                        <!-- Menu Structure Panel -->
                        <div class="menu-structure-panel">
                            <h5 class="mb-3">
                                <i class="fas fa-sitemap me-2"></i>Menu Structure
                            </h5>
                            
                            <div class="menu-structure" id="menuStructure">
                                <?php if (empty($menuItems)): ?>
                                <div class="empty-menu">
                                    <i class="fas fa-bars fa-3x mb-3"></i>
                                    <h6>No menu items yet</h6>
                                    <p class="text-muted">Add pages or custom links to build your menu</p>
                                </div>
                                <?php else: ?>
                                    <?php echo renderMenuItems($menuItems); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let menuId = <?= $menu['id'] ?>;
        let menuItemCounter = 1000; // For temporary IDs
        
        // Initialize Sortable for menu structure
        const menuStructure = document.getElementById('menuStructure');
        const sortable = Sortable.create(menuStructure, {
            group: 'menu',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onEnd: function(evt) {
                updateMenuOrder();
            }
        });

        // Add custom link to menu
        document.getElementById('customLinkForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const title = document.getElementById('customTitle').value;
            const url = document.getElementById('customUrl').value;
            
            if (title && url) {
                addMenuItemToStructure({
                    id: 'temp_' + (++menuItemCounter),
                    title: title,
                    url: url,
                    type: 'custom',
                    page_id: null,
                    status: 'active'
                });
                
                // Clear form
                document.getElementById('customTitle').value = '';
                document.getElementById('customUrl').value = '';
            }
        });

        // Add page to menu
        function addPageToMenu(pageId, title, url) {
            addMenuItemToStructure({
                id: 'temp_' + (++menuItemCounter),
                title: title,
                url: url,
                type: 'page',
                page_id: pageId,
                status: 'active'
            });
        }

        // Add menu item to structure
        function addMenuItemToStructure(item) {
            const menuStructure = document.getElementById('menuStructure');
            
            // Remove empty state if present
            const emptyMenu = menuStructure.querySelector('.empty-menu');
            if (emptyMenu) {
                emptyMenu.remove();
            }
            
            const itemElement = createMenuItemElement(item);
            menuStructure.appendChild(itemElement);
        }

        // Create menu item element
        function createMenuItemElement(item) {
            const div = document.createElement('div');
            div.className = 'menu-structure-item';
            div.dataset.itemId = item.id;
            div.dataset.type = item.type;
            div.dataset.pageId = item.page_id || '';
            
            const typeClass = item.type === 'page' ? 'bg-primary' : 'bg-secondary';
            
            div.innerHTML = `
                <div class="item-controls">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editMenuItem('${item.id}')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeMenuItem('${item.id}')" title="Remove">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <strong>${item.title}</strong>
                        <br>
                        <small class="text-muted">${item.url}</small>
                        <br>
                        <span class="badge ${typeClass} item-type-badge">${item.type}</span>
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-grip-vertical"></i>
                    </div>
                </div>
            `;
            
            return div;
        }

        // Remove menu item
        function removeMenuItem(itemId) {
            if (confirm('Are you sure you want to remove this menu item?')) {
                const item = document.querySelector(`[data-item-id="${itemId}"]`);
                if (item) {
                    item.remove();
                    
                    // Show empty state if no items left
                    const menuStructure = document.getElementById('menuStructure');
                    if (menuStructure.children.length === 0) {
                        menuStructure.innerHTML = `
                            <div class="empty-menu">
                                <i class="fas fa-bars fa-3x mb-3"></i>
                                <h6>No menu items yet</h6>
                                <p class="text-muted">Add pages or custom links to build your menu</p>
                            </div>
                        `;
                    }
                }
            }
        }

        // Edit menu item (placeholder)
        function editMenuItem(itemId) {
            alert('Edit functionality will be implemented in the next iteration');
        }

        // Update menu order
        function updateMenuOrder() {
            // This would send the new order to the server
            console.log('Menu order updated');
        }

        // Save menu structure
        function saveMenuStructure() {
            const items = [];
            const menuItems = document.querySelectorAll('.menu-structure-item');
            
            menuItems.forEach((item, index) => {
                items.push({
                    id: item.dataset.itemId,
                    title: item.querySelector('strong').textContent,
                    url: item.querySelector('small').textContent,
                    type: item.dataset.type,
                    page_id: item.dataset.pageId || null,
                    sort_order: index,
                    parent_id: null, // For now, no nesting
                    status: 'active'
                });
            });
            
            // Send to server
            fetch(`<?= base_url('admin/menu-items/update-order') ?>`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(items)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Menu saved successfully!');
                } else {
                    alert('Failed to save menu: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving the menu');
            });
        }
    </script>
</body>
</html>

<?php
// Helper function to render menu items
function renderMenuItems($items, $level = 0) {
    $html = '';
    foreach ($items as $item) {
        $typeClass = $item['type'] === 'page' ? 'bg-primary' : 'bg-secondary';
        $indent = $level > 0 ? 'style="margin-left: ' . ($level * 30) . 'px;"' : '';
        
        $html .= '<div class="menu-structure-item" data-item-id="' . $item['id'] . '" data-type="' . $item['type'] . '" data-page-id="' . ($item['page_id'] ?? '') . '" ' . $indent . '>';
        $html .= '<div class="item-controls">';
        $html .= '<button type="button" class="btn btn-sm btn-outline-primary" onclick="editMenuItem(\'' . $item['id'] . '\')" title="Edit"><i class="fas fa-edit"></i></button>';
        $html .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="removeMenuItem(\'' . $item['id'] . '\')" title="Remove"><i class="fas fa-trash"></i></button>';
        $html .= '</div>';
        $html .= '<div class="d-flex justify-content-between align-items-start">';
        $html .= '<div>';
        $html .= '<strong>' . esc($item['title']) . '</strong><br>';
        $html .= '<small class="text-muted">' . esc($item['url']) . '</small><br>';
        $html .= '<span class="badge ' . $typeClass . ' item-type-badge">' . $item['type'] . '</span>';
        $html .= '</div>';
        $html .= '<div class="text-muted"><i class="fas fa-grip-vertical"></i></div>';
        $html .= '</div>';
        $html .= '</div>';
        
        // Render children if any
        if (!empty($item['children'])) {
            $html .= renderMenuItems($item['children'], $level + 1);
        }
    }
    return $html;
}
?>
