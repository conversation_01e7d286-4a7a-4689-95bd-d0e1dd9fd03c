<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Navigation Menus<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<span>Navigation Menus</span>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Navigation Menus</h1>
        <p class="page-subtitle">Create and manage navigation menus for your website</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/menus/create') ?>" class="btn btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Create New Menu
        </a>
    </div>
</div>
<!-- Menus Table -->
<div class="admin-table">
    <table id="menusTable" class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Menu Name</th>
                                            <th>Location</th>
                                            <th>Status</th>
                                            <th>Items</th>
                                            <th>Created By</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($menus as $menu): ?>
                                        <tr>
                                            <td>
                                                <strong><?= esc($menu['name']) ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-link me-1"></i>
                                                    <?= esc($menu['slug']) ?>
                                                </small>
                                                <?php if (!empty($menu['description'])): ?>
                                                <br>
                                                <small class="text-muted"><?= esc($menu['description']) ?></small>
                                                <?php endif; ?>
                                            </td>
                        <td>
                            <?php
                            $locationClass = match($menu['location']) {
                                'primary' => 'bg-primary',
                                'secondary' => 'bg-secondary',
                                'footer' => 'bg-dark',
                                'sidebar' => 'bg-info',
                                default => 'bg-secondary'
                            };
                            ?>
                            <span class="badge <?= $locationClass ?>">
                                <?= ucfirst($menu['location']) ?>
                            </span>
                        </td>
                        <td>
                            <span class="status-badge <?= $menu['status'] ?>">
                                <?= ucfirst($menu['status']) ?>
                            </span>
                        </td>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    <i class="fas fa-list me-1"></i>
                                                    0 items
                                                </span>
                                            </td>
                                            <td>
                                                <?= esc($menu['first_name'] . ' ' . $menu['last_name']) ?>
                                                <br>
                                                <small class="text-muted">@<?= esc($menu['username']) ?></small>
                                            </td>
                                            <td>
                                                <?= date('M j, Y', strtotime($menu['created_at'])) ?>
                                                <br>
                                                <small class="text-muted">
                                                    <?= date('g:i A', strtotime($menu['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="<?= base_url('admin/menus/builder/' . $menu['id']) ?>" 
                                                       class="btn btn-sm btn-outline-success" title="Menu Builder">
                                                        <i class="fas fa-sitemap"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/menus/edit/' . $menu['id']) ?>" 
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                                            onclick="duplicateMenu(<?= $menu['id'] ?>)" title="Duplicate">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteMenu(<?= $menu['id'] ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Initialize DataTable
    $(document).ready(function() {
        $('#menusTable').DataTable({
            responsive: true,
            order: [[5, 'desc']],
            pageLength: 25,
            language: {
                search: "Search menus:",
                lengthMenu: "Show _MENU_ menus per page",
                info: "Showing _START_ to _END_ of _TOTAL_ menus",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            }
        });
    });

    function deleteMenu(id) {
        confirmDelete('Are you sure you want to delete this menu? This action cannot be undone.', function() {
            adminAPI(`<?= base_url('admin/menus/delete/') ?>${id}`, {
                method: 'DELETE'
            })
            .then(data => {
                if (data.success) {
                    showAlert(data.message || 'Menu deleted successfully!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert(data.message || 'Failed to delete menu', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('An error occurred while deleting the menu', 'error');
            });
        });
    }

    function duplicateMenu(id) {
        confirmAction('Are you sure you want to duplicate this menu?', function() {
            adminAPI(`<?= base_url('admin/menus/duplicate/') ?>${id}`, {
                method: 'POST'
            })
            .then(data => {
                if (data.success) {
                    showAlert(data.message || 'Menu duplicated successfully!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert(data.message || 'Failed to duplicate menu', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('An error occurred while duplicating the menu', 'error');
            });
        });
    }
</script>
<?= $this->endSection() ?>
