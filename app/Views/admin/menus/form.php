<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/menus') ?>">Navigation Menus</a>
<i class="fas fa-chevron-right"></i>
<span><?= $title ?></span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<style>
    .slug-preview {
        font-family: monospace;
        background: var(--admin-light);
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid var(--admin-border);
        color: #6c757d;
        display: inline-block;
    }

    .location-info {
        background: rgba(var(--admin-info-rgb), 0.1);
        border: 1px solid rgba(var(--admin-info-rgb), 0.2);
        border-radius: 6px;
        padding: 12px;
        margin-top: 12px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title"><?= $title ?></h1>
        <p class="page-subtitle">Configure menu settings and structure</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/menus') ?>" class="btn btn-admin-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Menus
        </a>
    </div>
</div>
<!-- Menu Form -->
<form action="<?= $menu ? base_url('admin/menus/update/' . $menu['id']) : base_url('admin/menus/store') ?>"
      method="post" id="menuForm">

    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Basic Information
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="name" class="admin-form-label">Menu Name *</label>
                        <input type="text" class="admin-form-control" id="name" name="name"
                               value="<?= old('name', $menu['name'] ?? '') ?>" required>
                        <div class="admin-form-text">A descriptive name for your menu</div>
                    </div>

                    <div class="admin-form-group">
                        <label for="slug" class="admin-form-label">Menu Slug</label>
                        <input type="text" class="admin-form-control" id="slug" name="slug"
                               value="<?= old('slug', $menu['slug'] ?? '') ?>">
                        <div class="admin-form-text">
                            <strong>Identifier:</strong>
                            <span class="slug-preview" id="slugPreview">
                                <span id="slugText"><?= old('slug', $menu['slug'] ?? 'menu-slug') ?></span>
                            </span>
                        </div>
                    </div>

                    <div class="admin-form-group mb-0">
                        <label for="description" class="admin-form-label">Description</label>
                        <textarea class="admin-form-control" id="description" name="description" rows="3"><?= old('description', $menu['description'] ?? '') ?></textarea>
                        <div class="admin-form-text">Optional description for this menu</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Menu Settings -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Menu Settings
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="location" class="admin-form-label">Menu Location *</label>
                        <select class="admin-form-control" id="location" name="location" required>
                            <option value="">Select Location</option>
                            <option value="primary" <?= old('location', $menu['location'] ?? '') === 'primary' ? 'selected' : '' ?>>Primary Navigation</option>
                            <option value="secondary" <?= old('location', $menu['location'] ?? '') === 'secondary' ? 'selected' : '' ?>>Secondary Navigation</option>
                            <option value="footer" <?= old('location', $menu['location'] ?? '') === 'footer' ? 'selected' : '' ?>>Footer Menu</option>
                            <option value="sidebar" <?= old('location', $menu['location'] ?? '') === 'sidebar' ? 'selected' : '' ?>>Sidebar Menu</option>
                        </select>

                        <div class="location-info" id="locationInfo" style="display: none;">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <span id="locationDescription"></span>
                            </small>
                        </div>
                    </div>

                    <div class="admin-form-group">
                        <label for="status" class="admin-form-label">Status</label>
                        <select class="admin-form-control" id="status" name="status">
                            <option value="active" <?= old('status', $menu['status'] ?? 'active') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status', $menu['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                        <div class="admin-form-text">Only active menus are displayed on the website</div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-admin-primary">
                            <i class="fas fa-save me-2"></i>
                            <?= $menu ? 'Update Menu' : 'Create Menu' ?>
                        </button>

                        <?php if ($menu): ?>
                        <a href="<?= base_url('admin/menus/builder/' . $menu['id']) ?>" class="btn btn-admin-success">
                            <i class="fas fa-sitemap me-2"></i>
                            Open Menu Builder
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Auto-generate slug from name
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const slug = name.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');

        if (!document.getElementById('slug').value || document.getElementById('slug').dataset.auto !== 'false') {
            document.getElementById('slug').value = slug;
            document.getElementById('slugText').textContent = slug || 'menu-slug';
            document.getElementById('slug').dataset.auto = 'true';
        }
    });

    // Update slug preview when manually edited
    document.getElementById('slug').addEventListener('input', function() {
        document.getElementById('slugText').textContent = this.value || 'menu-slug';
        this.dataset.auto = 'false';
    });

    // Location information
    const locationDescriptions = {
        'primary': 'Main navigation menu, typically displayed in the header',
        'secondary': 'Secondary navigation, often used for utility links',
        'footer': 'Footer navigation menu, displayed at the bottom of pages',
        'sidebar': 'Sidebar navigation, typically used in widget areas'
    };

    document.getElementById('location').addEventListener('change', function() {
        const location = this.value;
        const infoDiv = document.getElementById('locationInfo');
        const descSpan = document.getElementById('locationDescription');

        if (location && locationDescriptions[location]) {
            descSpan.textContent = locationDescriptions[location];
            infoDiv.style.display = 'block';
        } else {
            infoDiv.style.display = 'none';
        }
    });

    // Trigger location change on page load
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('location').dispatchEvent(new Event('change'));
    });

    // Enhanced form validation
    document.getElementById('menuForm').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const location = document.getElementById('location').value;

        if (!name) {
            e.preventDefault();
            showAlert('Please enter a menu name.', 'error');
            document.getElementById('name').focus();
            return false;
        }

        if (!location) {
            e.preventDefault();
            showAlert('Please select a menu location.', 'error');
            document.getElementById('location').focus();
            return false;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
        submitBtn.disabled = true;

        // Re-enable button after a delay (in case of validation errors)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });
</script>
<?= $this->endSection() ?>
