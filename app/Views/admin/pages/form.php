<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/pages') ?>">Pages</a>
<i class="fas fa-chevron-right"></i>
<span><?= $title ?></span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<!-- Quill.js - Open Source WYSIWYG Editor -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
    .slug-preview {
        font-family: monospace;
        background: var(--admin-light);
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid var(--admin-border);
        color: #6c757d;
        display: inline-block;
    }

    .seo-preview {
        background: var(--admin-light);
        border: 1px solid var(--admin-border);
        border-radius: 8px;
        padding: 16px;
    }

    .seo-title {
        color: var(--admin-primary);
        font-size: 18px;
        line-height: 1.2;
        margin-bottom: 4px;
    }

    .seo-url {
        color: var(--admin-success);
        font-size: 14px;
        margin-bottom: 8px;
    }

    .seo-description {
        color: #6c757d;
        font-size: 14px;
        line-height: 1.4;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title"><?= $title ?></h1>
        <p class="page-subtitle">Create and manage your website pages</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/pages') ?>" class="btn btn-admin-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Pages
        </a>
    </div>
</div>
<!-- Page Form -->
<form action="<?= $page ? base_url('admin/pages/update/' . $page['id']) : base_url('admin/pages/store') ?>"
      method="post" id="pageForm">

    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Basic Information
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="title" class="admin-form-label">Page Title *</label>
                        <input type="text" class="admin-form-control" id="title" name="title"
                               value="<?= old('title', $page['title'] ?? '') ?>" required>
                    </div>

                    <div class="admin-form-group">
                        <label for="slug" class="admin-form-label">URL Slug</label>
                        <input type="text" class="admin-form-control" id="slug" name="slug"
                               value="<?= old('slug', $page['slug'] ?? '') ?>">
                        <div class="admin-form-text">
                            <strong>Preview:</strong>
                            <span class="slug-preview" id="slugPreview">
                                <?= base_url() ?><span id="slugText"><?= old('slug', $page['slug'] ?? 'page-title') ?></span>
                            </span>
                        </div>
                    </div>

                    <div class="admin-form-group mb-0">
                        <label for="excerpt" class="admin-form-label">Excerpt</label>
                        <textarea class="admin-form-control" id="excerpt" name="excerpt" rows="3"
                                  maxlength="500"><?= old('excerpt', $page['excerpt'] ?? '') ?></textarea>
                        <div class="admin-form-text">Brief description of the page (max 500 characters)</div>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Content
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div id="quill-editor" style="height: 400px;"></div>
                    <textarea id="content" name="content" style="display: none;"><?= old('content', $page['content'] ?? '') ?></textarea>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Publish Settings -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Publish Settings
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="status" class="admin-form-label">Status</label>
                        <select class="admin-form-control" id="status" name="status">
                            <option value="draft" <?= old('status', $page['status'] ?? 'draft') === 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="published" <?= old('status', $page['status'] ?? '') === 'published' ? 'selected' : '' ?>>Published</option>
                            <option value="private" <?= old('status', $page['status'] ?? '') === 'private' ? 'selected' : '' ?>>Private</option>
                        </select>
                    </div>

                    <div class="admin-form-group">
                        <label for="published_at" class="admin-form-label">Publish Date</label>
                        <input type="datetime-local" class="admin-form-control" id="published_at" name="published_at"
                               value="<?= old('published_at', $page['published_at'] ?? '') ?>">
                    </div>

                    <div class="admin-form-group">
                        <label for="template" class="admin-form-label">Template</label>
                        <select class="admin-form-control" id="template" name="template">
                            <option value="default" <?= old('template', $page['template'] ?? 'default') === 'default' ? 'selected' : '' ?>>Default</option>
                            <option value="full-width" <?= old('template', $page['template'] ?? '') === 'full-width' ? 'selected' : '' ?>>Full Width</option>
                            <option value="sidebar" <?= old('template', $page['template'] ?? '') === 'sidebar' ? 'selected' : '' ?>>With Sidebar</option>
                        </select>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-admin-primary">
                            <i class="fas fa-save me-2"></i>
                            <?= $page ? 'Update Page' : 'Create Page' ?>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Page Hierarchy -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sitemap me-2"></i>Page Hierarchy
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="parent_id" class="admin-form-label">Parent Page</label>
                        <select class="admin-form-control" id="parent_id" name="parent_id">
                            <?php foreach ($parents as $value => $label): ?>
                                <option value="<?= $value ?>" <?= old('parent_id', $page['parent_id'] ?? '') == $value ? 'selected' : '' ?>>
                                    <?= esc($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="admin-form-group mb-0">
                        <label for="sort_order" class="admin-form-label">Sort Order</label>
                        <input type="number" class="admin-form-control" id="sort_order" name="sort_order"
                               value="<?= old('sort_order', $page['sort_order'] ?? 0) ?>" min="0">
                        <div class="admin-form-text">Lower numbers appear first</div>
                    </div>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-image me-2"></i>Featured Image
                    </h5>
                </div>
                <div class="admin-card-body">
                    <input type="hidden" id="featured_image" name="featured_image"
                           value="<?= old('featured_image', $page['featured_image'] ?? '') ?>">
                    <div id="imagePreview" class="mb-3" style="display: <?= old('featured_image', $page['featured_image'] ?? '') ? 'block' : 'none' ?>;">
                        <img id="previewImg" src="<?= old('featured_image', $page['featured_image'] ?? '') ?>"
                             class="img-thumbnail" style="max-width: 200px;">
                        <button type="button" class="btn btn-sm btn-admin-danger ms-2" onclick="removeImage()">
                            <i class="fas fa-times"></i> Remove
                        </button>
                    </div>
                    <button type="button" class="btn btn-admin-secondary" onclick="selectImage()">
                        <i class="fas fa-image me-2"></i>Select Image
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Settings -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>SEO Settings
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="meta_title" class="admin-form-label">Meta Title</label>
                                <input type="text" class="admin-form-control" id="meta_title" name="meta_title"
                                       value="<?= old('meta_title', $page['meta_title'] ?? '') ?>" maxlength="60">
                                <div class="admin-form-text">
                                    <span id="metaTitleCount">0</span>/60 characters
                                    <span class="text-muted">- Recommended: 50-60 characters</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="admin-form-group">
                                <label for="meta_keywords" class="admin-form-label">Meta Keywords</label>
                                <input type="text" class="admin-form-control" id="meta_keywords" name="meta_keywords"
                                       value="<?= old('meta_keywords', $page['meta_keywords'] ?? '') ?>">
                                <div class="admin-form-text">Separate keywords with commas</div>
                            </div>
                        </div>
                    </div>

                    <div class="admin-form-group">
                        <label for="meta_description" class="admin-form-label">Meta Description</label>
                        <textarea class="admin-form-control" id="meta_description" name="meta_description"
                                  rows="3" maxlength="160"><?= old('meta_description', $page['meta_description'] ?? '') ?></textarea>
                        <div class="admin-form-text">
                            <span id="metaDescCount">0</span>/160 characters
                            <span class="text-muted">- Recommended: 150-160 characters</span>
                        </div>
                    </div>

                    <!-- SEO Preview -->
                    <div class="mt-4">
                        <h6>Search Engine Preview:</h6>
                        <div class="seo-preview">
                            <div class="seo-title" id="seoTitle">
                                <?= esc($page['meta_title'] ?? $page['title'] ?? 'Page Title') ?>
                            </div>
                            <div class="seo-url" id="seoUrl">
                                <?= base_url() ?><span id="seoSlug"><?= esc($page['slug'] ?? 'page-slug') ?></span>
                            </div>
                            <div class="seo-description" id="seoDescription">
                                <?= esc($page['meta_description'] ?? $page['excerpt'] ?? 'Page description will appear here...') ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script>
        // Initialize Quill.js Editor
        var quill = new Quill('#quill-editor', {
            theme: 'snow',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'align': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'indent': '-1'}, { 'indent': '+1' }],
                    ['blockquote', 'code-block'],
                    ['link', 'image', 'video'],
                    ['clean']
                ]
            },
            placeholder: 'Write your page content here...'
        });

        // Sync Quill content with hidden textarea
        quill.on('text-change', function() {
            document.getElementById('content').value = quill.root.innerHTML;
        });

        // Load existing content into Quill
        var existingContent = document.getElementById('content').value;
        if (existingContent) {
            quill.root.innerHTML = existingContent;
        }

        // Auto-generate slug from title
        document.getElementById('title').addEventListener('input', function() {
            const title = this.value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');

            if (!document.getElementById('slug').value || document.getElementById('slug').dataset.auto !== 'false') {
                document.getElementById('slug').value = slug;
                document.getElementById('slugText').textContent = slug || 'page-title';
                document.getElementById('seoSlug').textContent = slug || 'page-slug';
                document.getElementById('slug').dataset.auto = 'true';
            }

            // Update SEO preview title if meta title is empty
            if (!document.getElementById('meta_title').value) {
                document.getElementById('seoTitle').textContent = title || 'Page Title';
            }
        });

        // Update slug preview when manually edited
        document.getElementById('slug').addEventListener('input', function() {
            const slug = this.value || 'page-title';
            document.getElementById('slugText').textContent = slug;
            document.getElementById('seoSlug').textContent = slug;
            this.dataset.auto = 'false';
        });

        // SEO character counters and preview updates
        function updateCharCount(inputId, countId) {
            const input = document.getElementById(inputId);
            const counter = document.getElementById(countId);

            function update() {
                const length = input.value.length;
                counter.textContent = length;

                // Color coding for optimal lengths
                if (inputId === 'meta_title') {
                    counter.className = length > 60 ? 'text-danger' : length > 50 ? 'text-warning' : 'text-success';
                } else if (inputId === 'meta_description') {
                    counter.className = length > 160 ? 'text-danger' : length > 150 ? 'text-warning' : 'text-success';
                }
            }

            input.addEventListener('input', update);
            update(); // Initial count
        }

        updateCharCount('meta_title', 'metaTitleCount');
        updateCharCount('meta_description', 'metaDescCount');

        // Update SEO preview
        document.getElementById('meta_title').addEventListener('input', function() {
            const title = this.value || document.getElementById('title').value || 'Page Title';
            document.getElementById('seoTitle').textContent = title;
        });

        document.getElementById('meta_description').addEventListener('input', function() {
            const desc = this.value || document.getElementById('excerpt').value || 'Page description will appear here...';
            document.getElementById('seoDescription').textContent = desc;
        });

        document.getElementById('excerpt').addEventListener('input', function() {
            if (!document.getElementById('meta_description').value) {
                document.getElementById('seoDescription').textContent = this.value || 'Page description will appear here...';
            }
        });

        // Image selection functions
        function selectImage() {
            // This would typically open a media library modal
            // For now, we'll use a simple prompt
            const imageUrl = prompt('Enter image URL:');
            if (imageUrl) {
                document.getElementById('featured_image').value = imageUrl;
                document.getElementById('previewImg').src = imageUrl;
                document.getElementById('imagePreview').style.display = 'block';
            }
        }

        function removeImage() {
            document.getElementById('featured_image').value = '';
            document.getElementById('imagePreview').style.display = 'none';
        }

        // Form validation and submission
        document.getElementById('pageForm').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();

            // Sync Quill content before submission
            document.getElementById('content').value = quill.root.innerHTML;

            if (!title) {
                e.preventDefault();
                showAlert('Please enter a page title.', 'error');
                document.getElementById('title').focus();
                return false;
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            submitBtn.disabled = true;

            // Re-enable button after delay (in case of validation errors)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });

        // Initialize character counts on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateCharCount('meta_title', 'metaTitleCount');
            updateCharCount('meta_description', 'metaDescCount');
        });
</script>
<?= $this->endSection() ?>
