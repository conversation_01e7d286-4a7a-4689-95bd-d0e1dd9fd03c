<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Pages<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<span>Pages</span>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Pages</h1>
        <p class="page-subtitle">Create and manage your website pages</p>
    </div>
    <div class="page-actions">
        <a href="<?= base_url('admin/pages/create') ?>" class="btn btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New Page
        </a>
    </div>
</div>
<!-- Pages Table -->
<div class="admin-table">
    <table id="pagesTable" class="table table-hover mb-0">
        <thead>
            <tr>
                <th>Title</th>
                <th>Author</th>
                <th>Status</th>
                <th>Created</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($pages as $page): ?>
            <tr>
                <td>
                    <strong><?= esc($page['title']) ?></strong>
                    <br>
                    <small class="text-muted">
                        <i class="fas fa-link me-1"></i>
                        <?= esc($page['slug']) ?>
                    </small>
                </td>
                <td>
                    <?= esc($page['first_name'] . ' ' . $page['last_name']) ?>
                    <br>
                    <small class="text-muted">@<?= esc($page['username']) ?></small>
                </td>
                <td>
                    <span class="status-badge <?= $page['status'] ?>">
                        <?= ucfirst($page['status']) ?>
                    </span>
                </td>
                <td>
                    <?= date('M j, Y', strtotime($page['created_at'])) ?>
                    <br>
                    <small class="text-muted">
                        <?= date('g:i A', strtotime($page['created_at'])) ?>
                    </small>
                </td>
                <td>
                    <div class="action-buttons">
                        <a href="<?= base_url('admin/pages/edit/' . $page['id']) ?>"
                           class="btn btn-sm btn-admin-secondary" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="<?= base_url('admin/pages/preview/' . $page['id']) ?>"
                           class="btn btn-sm btn-admin-info" title="Preview" target="_blank">
                            <i class="fas fa-eye"></i>
                        </a>
                        <button type="button" class="btn btn-sm btn-admin-danger"
                                onclick="deletePage(<?= $page['id'] ?>)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Initialize DataTable
    $(document).ready(function() {
        $('#pagesTable').DataTable({
            responsive: true,
            order: [[3, 'desc']],
            pageLength: 25,
            language: {
                search: "Search pages:",
                lengthMenu: "Show _MENU_ pages per page",
                info: "Showing _START_ to _END_ of _TOTAL_ pages",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            }
        });
    });

    function deletePage(id) {
        confirmDelete('Are you sure you want to delete this page? This action cannot be undone.', function() {
            adminAPI(`<?= base_url('admin/pages/delete/') ?>${id}`, {
                method: 'DELETE'
            })
            .then(data => {
                if (data.success) {
                    showAlert(data.message || 'Page deleted successfully!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert(data.message || 'Failed to delete page', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('An error occurred while deleting the page', 'error');
            });
        });
    }
</script>
<?= $this->endSection() ?>
