<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->renderSection('title', true) ?> - CMS Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .sidebar.collapsed {
            width: 70px;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 10px;
        }
        .sidebar.collapsed .nav-link span {
            display: none;
        }
        .main-content {
            margin-left: 250px;
            transition: all 0.3s ease;
            min-height: 100vh;
            background: #f8f9fa;
        }
        .main-content.expanded {
            margin-left: 70px;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        .content-area {
            padding: 20px;
        }
        .sidebar-toggle {
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.2rem;
        }
        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 10px;
        }
        .sidebar-brand h4 {
            color: white;
            margin: 0;
            font-weight: bold;
        }
        .sidebar.collapsed .sidebar-brand h4 span {
            display: none;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }
            .main-content {
                margin-left: 70px;
            }
            .sidebar .nav-link span {
                display: none;
            }
        }
    </style>
    <?= $this->renderSection('head') ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-brand">
            <h4>
                <i class="fas fa-cogs me-2"></i>
                <span>CMS Admin</span>
            </h4>
        </div>
        
        <nav class="nav flex-column">
            <a class="nav-link <?= (current_url() == base_url('admin/dashboard')) ? 'active' : '' ?>" 
               href="<?= base_url('admin/dashboard') ?>">
                <i class="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
            </a>
            
            <!-- Content Management -->
            <div class="nav-section mt-3">
                <small class="text-white-50 px-3 mb-2 d-block">
                    <span>CONTENT</span>
                </small>
                
                <?php if (in_array($user['role'], ['admin', 'editor'])): ?>
                <a class="nav-link <?= (strpos(current_url(), 'admin/pages') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/pages') ?>">
                    <i class="fas fa-file-alt"></i>
                    <span>Pages</span>
                </a>
                <?php endif; ?>
                
                <a class="nav-link <?= (strpos(current_url(), 'admin/blog') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/blog') ?>">
                    <i class="fas fa-blog"></i>
                    <span>Blog Posts</span>
                </a>
                
                <a class="nav-link <?= (strpos(current_url(), 'admin/media') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/media') ?>">
                    <i class="fas fa-images"></i>
                    <span>Media Library</span>
                </a>
                
                <?php if (in_array($user['role'], ['admin', 'editor'])): ?>
                <a class="nav-link <?= (strpos(current_url(), 'admin/menus') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/menus') ?>">
                    <i class="fas fa-bars"></i>
                    <span>Menus</span>
                </a>
                <?php endif; ?>
            </div>
            
            <!-- Appearance -->
            <div class="nav-section mt-3">
                <small class="text-white-50 px-3 mb-2 d-block">
                    <span>APPEARANCE</span>
                </small>

                <a class="nav-link <?= (strpos(current_url(), 'admin/themes') !== false && strpos(current_url(), 'marketplace') === false) ? 'active' : '' ?>"
                   href="<?= base_url('admin/themes') ?>">
                    <i class="fas fa-paint-brush"></i>
                    <span>Themes</span>
                </a>

                <a class="nav-link <?= (strpos(current_url(), 'templates/marketplace') !== false) ? 'active' : '' ?>"
                   href="<?= base_url('admin/templates/marketplace') ?>">
                    <i class="fas fa-store"></i>
                    <span>Template Store</span>
                </a>
            </div>
            
            <!-- Users & Settings -->
            <?php if ($user['role'] === 'admin'): ?>
            <div class="nav-section mt-3">
                <small class="text-white-50 px-3 mb-2 d-block">
                    <span>ADMINISTRATION</span>
                </small>
                
                <a class="nav-link <?= (strpos(current_url(), 'admin/users') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/users') ?>">
                    <i class="fas fa-users"></i>
                    <span>Users</span>
                </a>
                
                <a class="nav-link <?= (strpos(current_url(), 'admin/settings') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/settings') ?>">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
            <?php endif; ?>
            
            <!-- Quick Actions -->
            <div class="nav-section mt-4">
                <small class="text-white-50 px-3 mb-2 d-block">
                    <span>QUICK ACTIONS</span>
                </small>
                
                <a class="nav-link" href="<?= base_url() ?>" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    <span>View Site</span>
                </a>
                
                <a class="nav-link" href="<?= base_url('user/dashboard') ?>">
                    <i class="fas fa-user"></i>
                    <span>My Profile</span>
                </a>
            </div>
            
            <!-- Logout -->
            <div class="mt-auto mb-3">
                <a class="nav-link" href="<?= base_url('auth/logout') ?>">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <button class="sidebar-toggle me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <?= $this->renderSection('breadcrumb') ?>
                    </ol>
                </nav>
            </div>
            
            <div class="d-flex align-items-center">
                <!-- Quick Add Dropdown -->
                <div class="dropdown me-3">
                    <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-plus me-1"></i>Quick Add
                    </button>
                    <ul class="dropdown-menu">
                        <?php if (in_array($user['role'], ['admin', 'editor', 'author'])): ?>
                        <li><a class="dropdown-item" href="<?= base_url('admin/blog/create') ?>">
                            <i class="fas fa-blog me-2"></i>New Blog Post</a></li>
                        <?php endif; ?>
                        
                        <?php if (in_array($user['role'], ['admin', 'editor'])): ?>
                        <li><a class="dropdown-item" href="<?= base_url('admin/pages/create') ?>">
                            <i class="fas fa-file-alt me-2"></i>New Page</a></li>
                        <?php endif; ?>
                        
                        <li><a class="dropdown-item" href="<?= base_url('admin/media') ?>">
                            <i class="fas fa-upload me-2"></i>Upload Media</a></li>
                        
                        <?php if ($user['role'] === 'admin'): ?>
                        <li><a class="dropdown-item" href="<?= base_url('admin/users/create') ?>">
                            <i class="fas fa-user-plus me-2"></i>New User</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <!-- User Dropdown -->
                <div class="dropdown">
                    <button class="btn btn-link text-decoration-none dropdown-toggle d-flex align-items-center" 
                            type="button" data-bs-toggle="dropdown">
                        <div class="user-avatar me-2">
                            <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                        </div>
                        <div class="text-start">
                            <div class="fw-bold text-dark"><?= esc($user['first_name'] . ' ' . $user['last_name']) ?></div>
                            <small class="text-muted"><?= ucfirst($user['role']) ?></small>
                        </div>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?= base_url('user/profile') ?>">
                            <i class="fas fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="<?= base_url('user/settings') ?>">
                            <i class="fas fa-cog me-2"></i>Account Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?= base_url() ?>" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Website</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?= base_url('auth/logout') ?>">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Content Area -->
        <div class="content-area">
            <!-- Flash Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= $error ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Page Content -->
            <?= $this->renderSection('content') ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // Restore sidebar state
        document.addEventListener('DOMContentLoaded', function() {
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
                document.getElementById('mainContent').classList.add('expanded');
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);

        // Global alert function
        function showAlert(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
            const icon = type === 'error' ? 'fas fa-exclamation-triangle' :
                        type === 'success' ? 'fas fa-check-circle' : 'fas fa-info-circle';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show">
                    <i class="${icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            $('.content-area').prepend(alertHtml);

            // Auto-hide after 5 seconds
            setTimeout(function() {
                $('.alert').first().fadeOut();
            }, 5000);
        }

        // Debounce function for search
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>

    <?= $this->renderSection('scripts') ?>
</body>
</html>
