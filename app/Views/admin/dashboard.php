<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Dashboard<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<span>Dashboard</span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<style>
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: transform 0.3s ease;
        height: 100%;
    }
    .stats-card:hover {
        transform: translateY(-5px);
    }
    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
    }
    .activity-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        height: 100%;
    }
    .welcome-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Dashboard</h1>
        <p class="page-subtitle">Welcome back, <?= esc($user['first_name']) ?>! Here's what's happening with your CMS.</p>
    </div>
    <div class="page-actions">
        <div class="dropdown">
            <button class="btn btn-admin-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-plus me-2"></i>Quick Add
            </button>
            <ul class="dropdown-menu">
                <?php if (in_array($user['role'], ['admin', 'editor', 'author'])): ?>
                <li><a class="dropdown-item" href="<?= base_url('admin/blog/create') ?>">
                    <i class="fas fa-blog me-2"></i>New Blog Post</a></li>
                <?php endif; ?>

                <?php if (in_array($user['role'], ['admin', 'editor'])): ?>
                <li><a class="dropdown-item" href="<?= base_url('admin/pages/create') ?>">
                    <i class="fas fa-file-alt me-2"></i>New Page</a></li>
                <?php endif; ?>

                <li><a class="dropdown-item" href="<?= base_url('admin/media') ?>">
                    <i class="fas fa-upload me-2"></i>Upload Media</a></li>

                <?php if ($user['role'] === 'admin'): ?>
                <li><a class="dropdown-item" href="<?= base_url('admin/users/create') ?>">
                    <i class="fas fa-user-plus me-2"></i>New User</a></li>
                <?php endif; ?>
            </ul>
        </div>
        <a href="<?= base_url() ?>" target="_blank" class="btn btn-admin-primary">
            <i class="fas fa-external-link-alt me-2"></i>View Site
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon bg-primary">
            <i class="fas fa-blog"></i>
        </div>
        <div class="stat-value"><?= $stats['total_posts'] ?? 0 ?></div>
        <div class="stat-label">Total Blog Posts</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>+<?= $stats['posts_this_month'] ?? 0 ?> this month</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon bg-success">
            <i class="fas fa-eye"></i>
        </div>
        <div class="stat-value"><?= number_format($stats['total_views'] ?? 0) ?></div>
        <div class="stat-label">Total Views</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>+<?= number_format($stats['views_this_week'] ?? 0) ?> this week</span>
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-icon bg-info">
            <i class="fas fa-images"></i>
        </div>
        <div class="stat-value"><?= $stats['total_media'] ?? 0 ?></div>
        <div class="stat-label">Media Files</div>
        <div class="stat-change">
            <i class="fas fa-hdd"></i>
            <span><?= $stats['media_size'] ?? '0 MB' ?> used</span>
        </div>
    </div>

    <?php if ($user['role'] === 'admin'): ?>
    <div class="stat-card">
        <div class="stat-icon bg-warning">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-value"><?= $stats['total_users'] ?? 0 ?></div>
        <div class="stat-label">Total Users</div>
        <div class="stat-change positive">
            <i class="fas fa-user-plus"></i>
            <span>+<?= $stats['new_users_this_month'] ?? 0 ?> this month</span>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="activity-card">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5><i class="fas fa-clock me-2"></i>Recent Pages</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_pages)): ?>
                    <?php foreach ($recent_pages as $page): ?>
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 40px;">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">
                                <a href="<?= base_url('admin/pages/edit/' . $page['id']) ?>" class="text-decoration-none">
                                    <?= esc($page['title']) ?>
                                </a>
                            </h6>
                            <small class="text-muted">
                                <?= date('M j, Y', strtotime($page['created_at'])) ?> • 
                                <span class="badge bg-<?= $page['status'] === 'published' ? 'success' : 'warning' ?>">
                                    <?= ucfirst($page['status']) ?>
                                </span>
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted">No pages created yet.</p>
                <?php endif; ?>
                
                <div class="text-center">
                    <a href="<?= base_url('admin/pages') ?>" class="btn btn-outline-primary btn-sm">
                        View All Pages
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="activity-card">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5><i class="fas fa-blog me-2"></i>Recent Blog Posts</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_posts)): ?>
                    <?php foreach ($recent_posts as $post): ?>
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 40px;">
                                <i class="fas fa-blog"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">
                                <a href="<?= base_url('admin/blog/edit/' . $post['id']) ?>" class="text-decoration-none">
                                    <?= esc($post['title']) ?>
                                </a>
                            </h6>
                            <small class="text-muted">
                                <?= date('M j, Y', strtotime($post['created_at'])) ?> • 
                                <span class="badge bg-<?= $post['status'] === 'published' ? 'success' : 'warning' ?>">
                                    <?= ucfirst($post['status']) ?>
                                </span>
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted">No blog posts created yet.</p>
                <?php endif; ?>
                
                <div class="text-center">
                    <a href="<?= base_url('admin/blog') ?>" class="btn btn-outline-success btn-sm">
                        View All Posts
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="activity-card">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if (in_array($user['role'], ['admin', 'editor'])): ?>
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/pages/create') ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-file-plus me-2"></i>New Page
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/blog/create') ?>" class="btn btn-outline-success w-100">
                            <i class="fas fa-plus me-2"></i>New Post
                        </a>
                    </div>
                    
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/media') ?>" class="btn btn-outline-info w-100">
                            <i class="fas fa-upload me-2"></i>Upload Media
                        </a>
                    </div>
                    
                    <?php if ($user['role'] === 'admin'): ?>
                    <div class="col-md-3 mb-2">
                        <a href="<?= base_url('admin/users/create') ?>" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-user-plus me-2"></i>New User
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Auto-refresh stats every 30 seconds
    setInterval(function() {
        // You can add AJAX calls here to refresh stats
    }, 30000);
</script>
<?= $this->endSection() ?>
