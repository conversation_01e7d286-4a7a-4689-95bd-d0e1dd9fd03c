<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Frontend page routes
$routes->get('page/(:segment)', 'Home::page/$1');

// Frontend blog routes
$routes->get('blog', 'BlogController::index');
$routes->get('blog/page/(:num)', 'BlogController::index/$1');
$routes->get('blog/(:segment)', 'BlogController::post/$1');
$routes->get('category/(:segment)', 'BlogController::category/$1');
$routes->get('category/(:segment)/page/(:num)', 'BlogController::category/$1/$2');
$routes->get('tag/(:segment)', 'BlogController::tag/$1');
$routes->get('tag/(:segment)/page/(:num)', 'BlogController::tag/$1/$2');
$routes->post('blog/comment', 'BlogController::submitComment');

// SEO and Performance Routes
$routes->get('sitemap.xml', 'SitemapController::index');
$routes->get('robots.txt', 'SitemapController::robots');
$routes->get('rss.xml', 'SitemapController::rss');
$routes->get('feed.json', 'SitemapController::jsonFeed');
$routes->get('manifest.json', 'SitemapController::manifest');

// Authentication Routes
$routes->group('auth', function($routes) {
    $routes->get('login', 'AuthController::login');
    $routes->post('login', 'AuthController::processLogin');
    $routes->get('register', 'AuthController::register');
    $routes->post('register', 'AuthController::processRegister');
    $routes->get('logout', 'AuthController::logout');
    $routes->get('check', 'AuthController::checkAuth');
});

// User Dashboard Routes (Protected)
$routes->group('user', ['filter' => 'auth'], function($routes) {
    $routes->get('dashboard', 'UserDashboardController::index');
    $routes->get('profile', 'UserDashboardController::profile');
    $routes->post('profile', 'UserDashboardController::updateProfile');
    $routes->get('posts', 'UserDashboardController::myPosts');
    $routes->get('pages', 'UserDashboardController::myPages');
    $routes->get('media', 'UserDashboardController::myMedia');
    $routes->get('settings', 'UserDashboardController::settings');
});

// Admin Routes (Protected)
$routes->group('admin', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Admin\DashboardController::index'); // Default admin route
    $routes->get('dashboard', 'Admin\DashboardController::index');

    // Page Management
    $routes->group('pages', function($routes) {
        $routes->get('/', 'Admin\PageController::index');
        $routes->get('create', 'Admin\PageController::create', ['filter' => 'permission:pages.create']);
        $routes->post('store', 'Admin\PageController::store', ['filter' => 'permission:pages.create']);
        $routes->get('edit/(:num)', 'Admin\PageController::edit/$1', ['filter' => 'permission:pages.edit']);
        $routes->post('update/(:num)', 'Admin\PageController::update/$1', ['filter' => 'permission:pages.edit']);
        $routes->delete('delete/(:num)', 'Admin\PageController::delete/$1', ['filter' => 'permission:pages.delete']);
        $routes->get('preview/(:num)', 'Admin\PageController::preview/$1');
    });

    // Menu Management
    $routes->group('menus', function($routes) {
        $routes->get('/', 'Admin\MenuController::index');
        $routes->get('create', 'Admin\MenuController::create', ['filter' => 'permission:menus.create']);
        $routes->post('store', 'Admin\MenuController::store', ['filter' => 'permission:menus.create']);
        $routes->get('edit/(:num)', 'Admin\MenuController::edit/$1', ['filter' => 'permission:menus.edit']);
        $routes->post('update/(:num)', 'Admin\MenuController::update/$1', ['filter' => 'permission:menus.edit']);
        $routes->delete('delete/(:num)', 'Admin\MenuController::delete/$1', ['filter' => 'permission:menus.delete']);
        $routes->get('builder/(:num)', 'Admin\MenuController::builder/$1', ['filter' => 'permission:menus.edit']);
        $routes->post('duplicate/(:num)', 'Admin\MenuController::duplicate/$1', ['filter' => 'permission:menus.create']);
    });

    // Menu Item Management
    $routes->group('menu-items', function($routes) {
        $routes->post('store', 'Admin\MenuItemController::store', ['filter' => 'permission:menus.edit']);
        $routes->post('update/(:num)', 'Admin\MenuItemController::update/$1', ['filter' => 'permission:menus.edit']);
        $routes->delete('delete/(:num)', 'Admin\MenuItemController::delete/$1', ['filter' => 'permission:menus.edit']);
        $routes->get('get/(:num)', 'Admin\MenuItemController::get/$1');
        $routes->post('update-order', 'Admin\MenuItemController::updateOrder', ['filter' => 'permission:menus.edit']);
        $routes->get('menu/(:num)', 'Admin\MenuItemController::getMenuItems/$1');
        $routes->post('bulk-status', 'Admin\MenuItemController::bulkUpdateStatus', ['filter' => 'permission:menus.edit']);
        $routes->get('search-pages', 'Admin\MenuItemController::searchPages');
    });

    // Blog Management
    $routes->group('blog', function($routes) {
        $routes->get('/', 'Admin\BlogController::index');
        $routes->get('create', 'Admin\BlogController::create');
        $routes->post('store', 'Admin\BlogController::store');
        $routes->get('edit/(:num)', 'Admin\BlogController::edit/$1');
        $routes->post('update/(:num)', 'Admin\BlogController::update/$1');
        $routes->delete('delete/(:num)', 'Admin\BlogController::delete/$1');
        $routes->post('duplicate/(:num)', 'Admin\BlogController::duplicate/$1');
        $routes->post('bulk-action', 'Admin\BlogController::bulkAction');
        $routes->get('search', 'Admin\BlogController::search');
        $routes->get('by-status', 'Admin\BlogController::getPostsByStatus');
    });

    // Category Management
    $routes->group('categories', function($routes) {
        $routes->get('/', 'Admin\CategoryController::index');
        $routes->get('create', 'Admin\CategoryController::create');
        $routes->post('store', 'Admin\CategoryController::store');
        $routes->get('edit/(:num)', 'Admin\CategoryController::edit/$1');
        $routes->post('update/(:num)', 'Admin\CategoryController::update/$1');
        $routes->delete('delete/(:num)', 'Admin\CategoryController::delete/$1');
        $routes->post('reorder', 'Admin\CategoryController::reorder');
    });

    // Tag Management
    $routes->group('tags', function($routes) {
        $routes->get('/', 'Admin\TagController::index');
        $routes->get('create', 'Admin\TagController::create');
        $routes->post('store', 'Admin\TagController::store');
        $routes->get('edit/(:num)', 'Admin\TagController::edit/$1');
        $routes->post('update/(:num)', 'Admin\TagController::update/$1');
        $routes->delete('delete/(:num)', 'Admin\TagController::delete/$1');
        $routes->get('search', 'Admin\TagController::search');
    });

    // Comment Management
    $routes->group('comments', function($routes) {
        $routes->get('/', 'Admin\CommentController::index');
        $routes->get('pending', 'Admin\CommentController::pending');
        $routes->post('approve/(:num)', 'Admin\CommentController::approve/$1');
        $routes->post('spam/(:num)', 'Admin\CommentController::spam/$1');
        $routes->post('trash/(:num)', 'Admin\CommentController::trash/$1');
        $routes->delete('delete/(:num)', 'Admin\CommentController::delete/$1');
        $routes->post('bulk-action', 'Admin\CommentController::bulkAction');
    });

    // Media Management
    $routes->group('media', function($routes) {
        $routes->get('/', 'Admin\MediaController::index');
        $routes->post('upload', 'Admin\MediaController::upload');
        $routes->get('get-media', 'Admin\MediaController::getMedia');
        $routes->post('update/(:num)', 'Admin\MediaController::update/$1');
        $routes->delete('delete/(:num)', 'Admin\MediaController::delete/$1');
        $routes->post('bulk-delete', 'Admin\MediaController::bulkDelete');
        $routes->get('details/(:num)', 'Admin\MediaController::details/$1');
        $routes->get('picker', 'Admin\MediaController::picker');
        $routes->post('generate-thumbnails', 'Admin\MediaController::generateThumbnails');
        $routes->post('cleanup', 'Admin\MediaController::cleanup');
        $routes->get('stats', 'Admin\MediaController::stats');
    });

    // Theme Management
    $routes->group('themes', function($routes) {
        $routes->get('/', 'Admin\ThemeController::index');
        $routes->get('create', 'Admin\ThemeController::create');
        $routes->post('store', 'Admin\ThemeController::store');
        $routes->post('preview', 'Admin\ThemeController::preview');
        $routes->get('edit/(:num)', 'Admin\ThemeController::edit/$1');
        $routes->post('update/(:num)', 'Admin\ThemeController::update/$1');
        $routes->delete('delete/(:num)', 'Admin\ThemeController::delete/$1');
        $routes->post('set-default/(:num)', 'Admin\ThemeController::setDefault/$1');
        $routes->get('settings', 'Admin\ThemeController::settings');
        $routes->post('settings', 'Admin\ThemeController::updateSettings');
    });

    // Template Marketplace
    $routes->group('templates/marketplace', function($routes) {
        $routes->get('/', 'Admin\TemplateMarketplaceController::index');
        $routes->get('preview/(:segment)', 'Admin\TemplateMarketplaceController::preview/$1');
        $routes->post('install/(:segment)', 'Admin\TemplateMarketplaceController::install/$1');
    });

    // Template Customizer
    $routes->group('templates/customize', function($routes) {
        $routes->get('(:num)', 'Admin\TemplateCustomizerController::index/$1');
        $routes->post('save/(:num)', 'Admin\TemplateCustomizerController::save/$1');
        $routes->post('reset/(:num)', 'Admin\TemplateCustomizerController::reset/$1');
        $routes->get('export/(:num)', 'Admin\TemplateCustomizerController::export/$1');
        $routes->post('import/(:num)', 'Admin\TemplateCustomizerController::import/$1');
    });

    // Template Updates
    $routes->group('templates/updates', function($routes) {
        $routes->get('check', 'Admin\TemplateUpdateController::checkUpdates');
        $routes->post('update/(:num)', 'Admin\TemplateUpdateController::updateTemplate/$1');
        $routes->post('bulk-update', 'Admin\TemplateUpdateController::bulkUpdate');
        $routes->get('history/(:num)', 'Admin\TemplateUpdateController::getUpdateHistory/$1');
        $routes->post('rollback/(:num)', 'Admin\TemplateUpdateController::rollback/$1');
    });

    // Bulk Template Manager
    $routes->get('templates/bulk-manager', 'Admin\TemplateBulkController::index');

    // Template Analytics
    $routes->get('templates/analytics', function() {
        return view('admin/templates/analytics', [
            'title' => 'Template Analytics',
            'user' => [
                'id' => session()->get('user_id'),
                'username' => session()->get('username'),
                'email' => session()->get('email'),
                'role' => session()->get('role'),
                'first_name' => session()->get('first_name'),
                'last_name' => session()->get('last_name'),
            ]
        ]);
    });

    // User Management (Admin and Editor access)
    $routes->group('users', ['filter' => 'auth'], function($routes) {
        $routes->get('/', 'Admin\UserController::index');
        $routes->get('create', 'Admin\UserController::create');
        $routes->post('store', 'Admin\UserController::store');
        $routes->get('edit/(:num)', 'Admin\UserController::edit/$1');
        $routes->post('update/(:num)', 'Admin\UserController::update/$1');
        $routes->delete('delete/(:num)', 'Admin\UserController::delete/$1');
        $routes->post('toggle-status/(:num)', 'Admin\UserController::toggleStatus/$1');
        $routes->post('verify-email/(:num)', 'Admin\UserController::verifyEmail/$1');
        $routes->post('unverify-email/(:num)', 'Admin\UserController::unverifyEmail/$1');
        $routes->get('profile/(:num)', 'Admin\UserController::profile/$1');
        $routes->post('bulk-action', 'Admin\UserController::bulkAction');
    });
});
