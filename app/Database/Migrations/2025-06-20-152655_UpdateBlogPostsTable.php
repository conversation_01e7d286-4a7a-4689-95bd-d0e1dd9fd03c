<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdateBlogPostsTable extends Migration
{
    public function up()
    {
        // Check if blog_posts table exists, if not create it
        if (!$this->db->tableExists('blog_posts')) {
            $this->forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true,
                ],
                'title' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                ],
                'slug' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'unique' => true,
                ],
                'content' => [
                    'type' => 'LONGTEXT',
                    'null' => true,
                ],
                'excerpt' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'featured_image' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => true,
                ],
                'meta_title' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => true,
                ],
                'meta_description' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'meta_keywords' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'author_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                ],
                'category_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => true,
                ],
                'status' => [
                    'type' => 'ENUM',
                    'constraint' => ['draft', 'published', 'private', 'scheduled'],
                    'default' => 'draft',
                ],
                'comment_status' => [
                    'type' => 'ENUM',
                    'constraint' => ['open', 'closed'],
                    'default' => 'open',
                ],
                'view_count' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'default' => 0,
                ],
                'comment_count' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'default' => 0,
                ],
                'published_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
            ]);

            $this->forge->addKey('id', true);
            $this->forge->addKey('author_id');
            $this->forge->addKey('category_id');
            $this->forge->addKey('status');
            $this->forge->addKey('published_at');
            $this->forge->createTable('blog_posts');
        } else {
            // Table exists, check if we need to update the status column
            $fields = $this->db->getFieldData('blog_posts');
            $statusField = null;

            foreach ($fields as $field) {
                if ($field->name === 'status') {
                    $statusField = $field;
                    break;
                }
            }

            // If status field doesn't exist or needs updating, modify it
            if (!$statusField || strpos($statusField->type, 'scheduled') === false) {
                $this->forge->modifyColumn('blog_posts', [
                    'status' => [
                        'type' => 'ENUM',
                        'constraint' => ['draft', 'published', 'private', 'scheduled'],
                        'default' => 'draft',
                    ]
                ]);
            }

            // Add missing columns if they don't exist
            $columnNames = array_column($fields, 'name');

            if (!in_array('view_count', $columnNames)) {
                $this->forge->addColumn('blog_posts', [
                    'view_count' => [
                        'type' => 'INT',
                        'constraint' => 11,
                        'default' => 0,
                        'after' => 'comment_status'
                    ]
                ]);
            }

            if (!in_array('comment_count', $columnNames)) {
                $this->forge->addColumn('blog_posts', [
                    'comment_count' => [
                        'type' => 'INT',
                        'constraint' => 11,
                        'default' => 0,
                        'after' => 'view_count'
                    ]
                ]);
            }
        }
    }

    public function down()
    {
        // Revert status column back to original
        if ($this->db->tableExists('blog_posts')) {
            $this->forge->modifyColumn('blog_posts', [
                'status' => [
                    'type' => 'ENUM',
                    'constraint' => ['draft', 'published', 'private'],
                    'default' => 'draft',
                ]
            ]);
        }
    }
}
