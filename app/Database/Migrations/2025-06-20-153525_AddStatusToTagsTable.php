<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddStatusToTagsTable extends Migration
{
    public function up()
    {
        // Add status column to tags table
        $this->forge->addColumn('tags', [
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'default' => 'active',
                'after' => 'color'
            ]
        ]);

        // Update all existing tags to active status
        $this->db->query("UPDATE tags SET status = 'active' WHERE status IS NULL");
    }

    public function down()
    {
        // Remove status column
        $this->forge->dropColumn('tags', 'status');
    }
}
