<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class BlogSeeder extends Seeder
{
    public function run()
    {
        // Create categories if they don't exist
        $categories = [
            ['name' => 'Technology', 'slug' => 'technology', 'description' => 'Latest technology trends and insights'],
            ['name' => 'Web Development', 'slug' => 'web-development', 'description' => 'Web development tutorials and best practices'],
            ['name' => 'Design', 'slug' => 'design', 'description' => 'Design tips, trends, and inspiration'],
            ['name' => 'Business', 'slug' => 'business', 'description' => 'Business insights and strategies'],
        ];

        foreach ($categories as $category) {
            $existing = $this->db->table('categories')->where('slug', $category['slug'])->get()->getRowArray();
            if (!$existing) {
                $category['status'] = 'active';
                $category['created_at'] = date('Y-m-d H:i:s');
                $category['updated_at'] = date('Y-m-d H:i:s');
                $this->db->table('categories')->insert($category);
            }
        }

        // Create tags if they don't exist
        $tags = [
            ['name' => 'PHP', 'slug' => 'php'],
            ['name' => 'CodeIgniter', 'slug' => 'codeigniter'],
            ['name' => 'JavaScript', 'slug' => 'javascript'],
            ['name' => 'CSS', 'slug' => 'css'],
            ['name' => 'Bootstrap', 'slug' => 'bootstrap'],
            ['name' => 'MySQL', 'slug' => 'mysql'],
            ['name' => 'Tutorial', 'slug' => 'tutorial'],
            ['name' => 'Best Practices', 'slug' => 'best-practices'],
        ];

        foreach ($tags as $tag) {
            $existing = $this->db->table('tags')->where('slug', $tag['slug'])->get()->getRowArray();
            if (!$existing) {
                $tag['created_at'] = date('Y-m-d H:i:s');
                $tag['updated_at'] = date('Y-m-d H:i:s');
                $this->db->table('tags')->insert($tag);
            }
        }

        // Get category IDs
        $techCategory = $this->db->table('categories')->where('slug', 'technology')->get()->getRowArray();
        $webDevCategory = $this->db->table('categories')->where('slug', 'web-development')->get()->getRowArray();
        $designCategory = $this->db->table('categories')->where('slug', 'design')->get()->getRowArray();
        $businessCategory = $this->db->table('categories')->where('slug', 'business')->get()->getRowArray();

        // Create blog posts
        $posts = [
            [
                'title' => 'Getting Started with CodeIgniter 4: A Complete Guide',
                'slug' => 'getting-started-codeigniter-4-complete-guide',
                'content' => '<h2>Introduction to CodeIgniter 4</h2>
                <p>CodeIgniter 4 is a powerful PHP framework that makes web development faster and easier. In this comprehensive guide, we\'ll walk you through everything you need to know to get started with CodeIgniter 4.</p>
                
                <h3>What\'s New in CodeIgniter 4?</h3>
                <p>CodeIgniter 4 brings many exciting features and improvements:</p>
                <ul>
                    <li>Namespace support</li>
                    <li>PSR-4 autoloading</li>
                    <li>Improved performance</li>
                    <li>Better security features</li>
                    <li>Modern PHP features</li>
                </ul>
                
                <h3>Installation</h3>
                <p>You can install CodeIgniter 4 using Composer:</p>
                <pre><code>composer create-project codeigniter4/appstarter project-name</code></pre>
                
                <h3>Basic Configuration</h3>
                <p>After installation, you\'ll need to configure your database and environment settings. Update your <code>.env</code> file with your database credentials.</p>
                
                <h3>Creating Your First Controller</h3>
                <p>Controllers handle the logic of your application. Here\'s a simple example:</p>
                <pre><code>class Home extends BaseController
{
    public function index()
    {
        return view(\'welcome_message\');
    }
}</code></pre>
                
                <h3>Conclusion</h3>
                <p>CodeIgniter 4 is an excellent choice for PHP developers looking for a lightweight yet powerful framework. With its intuitive structure and comprehensive documentation, you\'ll be building amazing web applications in no time!</p>',
                'excerpt' => 'Learn how to get started with CodeIgniter 4, the modern PHP framework that makes web development faster and more enjoyable.',
                'meta_title' => 'CodeIgniter 4 Complete Beginner Guide - Getting Started Tutorial',
                'meta_description' => 'Complete beginner guide to CodeIgniter 4. Learn installation, configuration, and basic concepts to start building web applications.',
                'meta_keywords' => 'CodeIgniter 4, PHP framework, web development, tutorial, beginner guide',
                'status' => 'published',
                'author_id' => 1,
                'category_id' => $webDevCategory['id'],
                'published_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'view_count' => 245,
                'created_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
            ],
            [
                'title' => 'Modern CSS Techniques for Responsive Design',
                'slug' => 'modern-css-techniques-responsive-design',
                'content' => '<h2>The Evolution of CSS</h2>
                <p>CSS has come a long way since its early days. Modern CSS provides powerful tools for creating responsive, flexible layouts that work across all devices.</p>
                
                <h3>CSS Grid Layout</h3>
                <p>CSS Grid is a two-dimensional layout system that allows you to create complex layouts with ease:</p>
                <pre><code>.container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}</code></pre>
                
                <h3>Flexbox for Component Layout</h3>
                <p>Flexbox is perfect for one-dimensional layouts and component alignment:</p>
                <pre><code>.flex-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}</code></pre>
                
                <h3>CSS Custom Properties (Variables)</h3>
                <p>CSS variables make your stylesheets more maintainable:</p>
                <pre><code>:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
}

.button {
    background-color: var(--primary-color);
}</code></pre>
                
                <h3>Container Queries</h3>
                <p>Container queries allow components to respond to their container size rather than the viewport size, enabling truly modular design.</p>
                
                <h3>Best Practices</h3>
                <ul>
                    <li>Use mobile-first approach</li>
                    <li>Implement progressive enhancement</li>
                    <li>Test across different devices</li>
                    <li>Optimize for performance</li>
                </ul>',
                'excerpt' => 'Discover modern CSS techniques including Grid, Flexbox, and custom properties to create stunning responsive designs.',
                'meta_title' => 'Modern CSS Techniques for Responsive Web Design - 2025 Guide',
                'meta_description' => 'Learn modern CSS techniques for responsive design including CSS Grid, Flexbox, custom properties, and container queries.',
                'meta_keywords' => 'CSS, responsive design, CSS Grid, Flexbox, web design, modern CSS',
                'status' => 'published',
                'author_id' => 1,
                'category_id' => $designCategory['id'],
                'published_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'view_count' => 189,
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
            ],
            [
                'title' => 'Building Scalable Web Applications: Architecture Best Practices',
                'slug' => 'building-scalable-web-applications-architecture-best-practices',
                'content' => '<h2>The Importance of Scalable Architecture</h2>
                <p>As your web application grows, having a scalable architecture becomes crucial for maintaining performance and managing complexity.</p>
                
                <h3>Separation of Concerns</h3>
                <p>Organize your code into distinct layers:</p>
                <ul>
                    <li><strong>Presentation Layer:</strong> User interface and user experience</li>
                    <li><strong>Business Logic Layer:</strong> Core application logic</li>
                    <li><strong>Data Access Layer:</strong> Database interactions</li>
                </ul>
                
                <h3>Database Design Principles</h3>
                <p>A well-designed database is the foundation of a scalable application:</p>
                <ul>
                    <li>Normalize your data structure</li>
                    <li>Use appropriate indexes</li>
                    <li>Implement proper relationships</li>
                    <li>Consider caching strategies</li>
                </ul>
                
                <h3>API Design</h3>
                <p>Design RESTful APIs that are:</p>
                <ul>
                    <li>Consistent and predictable</li>
                    <li>Well-documented</li>
                    <li>Versioned properly</li>
                    <li>Secure and authenticated</li>
                </ul>
                
                <h3>Performance Optimization</h3>
                <p>Key strategies for optimal performance:</p>
                <ul>
                    <li>Implement caching at multiple levels</li>
                    <li>Optimize database queries</li>
                    <li>Use CDNs for static assets</li>
                    <li>Implement lazy loading</li>
                </ul>
                
                <h3>Security Considerations</h3>
                <p>Security should be built into your architecture from the ground up:</p>
                <ul>
                    <li>Input validation and sanitization</li>
                    <li>Authentication and authorization</li>
                    <li>HTTPS everywhere</li>
                    <li>Regular security audits</li>
                </ul>',
                'excerpt' => 'Learn essential architecture patterns and best practices for building web applications that can scale with your business.',
                'meta_title' => 'Scalable Web Application Architecture - Best Practices Guide',
                'meta_description' => 'Comprehensive guide to building scalable web applications with proper architecture, database design, and performance optimization.',
                'meta_keywords' => 'scalable architecture, web applications, best practices, performance, database design',
                'status' => 'published',
                'author_id' => 1,
                'category_id' => $techCategory['id'],
                'published_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'view_count' => 156,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
            ],
            [
                'title' => 'The Future of Web Development: Trends to Watch in 2025',
                'slug' => 'future-web-development-trends-2025',
                'content' => '<h2>Web Development in 2025</h2>
                <p>The web development landscape continues to evolve rapidly. Here are the key trends that will shape the industry in 2025 and beyond.</p>
                
                <h3>1. AI-Powered Development Tools</h3>
                <p>Artificial Intelligence is revolutionizing how we write code:</p>
                <ul>
                    <li>Code completion and generation</li>
                    <li>Automated testing and debugging</li>
                    <li>Performance optimization suggestions</li>
                    <li>Accessibility improvements</li>
                </ul>
                
                <h3>2. WebAssembly (WASM) Adoption</h3>
                <p>WebAssembly enables near-native performance in web browsers, opening new possibilities for web applications.</p>
                
                <h3>3. Progressive Web Apps (PWAs)</h3>
                <p>PWAs continue to bridge the gap between web and native applications with features like:</p>
                <ul>
                    <li>Offline functionality</li>
                    <li>Push notifications</li>
                    <li>App-like experience</li>
                    <li>Improved performance</li>
                </ul>
                
                <h3>4. Serverless Architecture</h3>
                <p>Serverless computing is changing how we deploy and scale applications:</p>
                <ul>
                    <li>Reduced operational overhead</li>
                    <li>Automatic scaling</li>
                    <li>Pay-per-use pricing</li>
                    <li>Faster time to market</li>
                </ul>
                
                <h3>5. Enhanced Security Focus</h3>
                <p>With increasing cyber threats, security is becoming more important than ever:</p>
                <ul>
                    <li>Zero-trust architecture</li>
                    <li>Advanced authentication methods</li>
                    <li>Privacy-first design</li>
                    <li>Compliance automation</li>
                </ul>
                
                <h3>Preparing for the Future</h3>
                <p>To stay ahead in web development:</p>
                <ul>
                    <li>Continuously learn new technologies</li>
                    <li>Focus on fundamentals</li>
                    <li>Embrace automation</li>
                    <li>Prioritize user experience</li>
                </ul>',
                'excerpt' => 'Explore the emerging trends and technologies that will define web development in 2025, from AI tools to serverless architecture.',
                'meta_title' => 'Web Development Trends 2025 - Future of Web Technology',
                'meta_description' => 'Discover the top web development trends for 2025 including AI tools, WebAssembly, PWAs, and serverless architecture.',
                'meta_keywords' => 'web development trends, 2025, AI, WebAssembly, PWA, serverless, future technology',
                'status' => 'published',
                'author_id' => 1,
                'category_id' => $techCategory['id'],
                'published_at' => date('Y-m-d H:i:s'),
                'view_count' => 89,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('blog_posts')->insertBatch($posts);

        // Get post IDs and create post-tag relationships
        $phpTag = $this->db->table('tags')->where('slug', 'php')->get()->getRowArray();
        $ciTag = $this->db->table('tags')->where('slug', 'codeigniter')->get()->getRowArray();
        $jsTag = $this->db->table('tags')->where('slug', 'javascript')->get()->getRowArray();
        $cssTag = $this->db->table('tags')->where('slug', 'css')->get()->getRowArray();
        $tutorialTag = $this->db->table('tags')->where('slug', 'tutorial')->get()->getRowArray();
        $bestPracticesTag = $this->db->table('tags')->where('slug', 'best-practices')->get()->getRowArray();

        $post1 = $this->db->table('blog_posts')->where('slug', 'getting-started-codeigniter-4-complete-guide')->get()->getRowArray();
        $post2 = $this->db->table('blog_posts')->where('slug', 'modern-css-techniques-responsive-design')->get()->getRowArray();
        $post3 = $this->db->table('blog_posts')->where('slug', 'building-scalable-web-applications-architecture-best-practices')->get()->getRowArray();
        $post4 = $this->db->table('blog_posts')->where('slug', 'future-web-development-trends-2025')->get()->getRowArray();

        // Create post-tag relationships
        $postTags = [
            ['post_id' => $post1['id'], 'tag_id' => $phpTag['id']],
            ['post_id' => $post1['id'], 'tag_id' => $ciTag['id']],
            ['post_id' => $post1['id'], 'tag_id' => $tutorialTag['id']],

            ['post_id' => $post2['id'], 'tag_id' => $cssTag['id']],
            ['post_id' => $post2['id'], 'tag_id' => $tutorialTag['id']],

            ['post_id' => $post3['id'], 'tag_id' => $bestPracticesTag['id']],
            ['post_id' => $post3['id'], 'tag_id' => $phpTag['id']],

            ['post_id' => $post4['id'], 'tag_id' => $jsTag['id']],
            ['post_id' => $post4['id'], 'tag_id' => $bestPracticesTag['id']],
        ];

        $this->db->table('post_tags')->insertBatch($postTags);
    }
}
