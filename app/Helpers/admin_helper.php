<?php

if (!function_exists('get_admin_stats')) {
    /**
     * Get basic admin statistics for navigation
     */
    function get_admin_stats()
    {
        static $stats = null;
        
        if ($stats === null) {
            $blogPostModel = new \App\Models\BlogPostModel();
            $userModel = new \App\Models\UserModel();
            $mediaModel = new \App\Models\MediaModel();
            
            $stats = [
                'total_posts' => $blogPostModel->countAllResults(),
                'total_users' => $userModel->countAllResults(),
                'total_media' => $mediaModel->countAllResults(),
            ];
        }
        
        return $stats;
    }
}

if (!function_exists('is_admin_route')) {
    /**
     * Check if current route is an admin route
     */
    function is_admin_route($route = null)
    {
        $currentUrl = current_url();
        
        if ($route) {
            return strpos($currentUrl, $route) !== false;
        }
        
        return strpos($currentUrl, '/admin/') !== false;
    }
}

if (!function_exists('admin_active_class')) {
    /**
     * Get active class for admin navigation
     */
    function admin_active_class($route, $class = 'active')
    {
        return is_admin_route($route) ? $class : '';
    }
}

if (!function_exists('format_file_size')) {
    /**
     * Format file size in human readable format
     */
    function format_file_size($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

if (!function_exists('time_ago')) {
    /**
     * Get time ago string
     */
    function time_ago($datetime)
    {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . ' minutes ago';
        if ($time < 86400) return floor($time/3600) . ' hours ago';
        if ($time < 2592000) return floor($time/86400) . ' days ago';
        
        return date('M j, Y', strtotime($datetime));
    }
}

if (!function_exists('admin_user_avatar')) {
    /**
     * Generate user avatar initials
     */
    function admin_user_avatar($firstName, $lastName = '')
    {
        $initials = strtoupper(substr($firstName, 0, 1));
        if ($lastName) {
            $initials .= strtoupper(substr($lastName, 0, 1));
        }
        return $initials;
    }
}

if (!function_exists('admin_status_badge')) {
    /**
     * Generate status badge HTML
     */
    function admin_status_badge($status, $text = null)
    {
        $text = $text ?: ucfirst($status);
        $class = 'status-badge ' . $status;
        
        return '<span class="' . $class . '">' . esc($text) . '</span>';
    }
}

if (!function_exists('admin_role_color')) {
    /**
     * Get color class for user role
     */
    function admin_role_color($role)
    {
        $colors = [
            'admin' => 'danger',
            'editor' => 'warning', 
            'author' => 'info',
            'subscriber' => 'secondary'
        ];
        
        return $colors[$role] ?? 'secondary';
    }
}

if (!function_exists('admin_breadcrumb')) {
    /**
     * Generate breadcrumb HTML
     */
    function admin_breadcrumb($items)
    {
        $html = '';
        $count = count($items);
        
        foreach ($items as $index => $item) {
            if ($index > 0) {
                $html .= '<i class="fas fa-chevron-right"></i>';
            }
            
            if ($index === $count - 1) {
                // Last item (current page)
                $html .= '<span>' . esc($item['title']) . '</span>';
            } else {
                // Link item
                $html .= '<a href="' . $item['url'] . '">' . esc($item['title']) . '</a>';
            }
        }
        
        return $html;
    }
}

if (!function_exists('admin_quick_actions')) {
    /**
     * Get quick actions based on user role
     */
    function admin_quick_actions($userRole)
    {
        $actions = [];
        
        // Blog post creation for authors and above
        if (in_array($userRole, ['admin', 'editor', 'author'])) {
            $actions[] = [
                'title' => 'New Blog Post',
                'url' => base_url('admin/blog/create'),
                'icon' => 'fas fa-blog',
                'color' => 'primary'
            ];
        }
        
        // Page creation for editors and above
        if (in_array($userRole, ['admin', 'editor'])) {
            $actions[] = [
                'title' => 'New Page',
                'url' => base_url('admin/pages/create'),
                'icon' => 'fas fa-file-alt',
                'color' => 'success'
            ];
        }
        
        // Media upload for all
        $actions[] = [
            'title' => 'Upload Media',
            'url' => base_url('admin/media'),
            'icon' => 'fas fa-upload',
            'color' => 'info'
        ];
        
        // User management for admins
        if ($userRole === 'admin') {
            $actions[] = [
                'title' => 'New User',
                'url' => base_url('admin/users/create'),
                'icon' => 'fas fa-user-plus',
                'color' => 'warning'
            ];
        }
        
        return $actions;
    }
}

if (!function_exists('admin_notification_count')) {
    /**
     * Get notification count (mock for now)
     */
    function admin_notification_count()
    {
        // This would typically query a notifications table
        return 3;
    }
}

if (!function_exists('admin_system_status')) {
    /**
     * Get system status indicators
     */
    function admin_system_status()
    {
        return [
            'database' => ['status' => 'success', 'text' => 'Connected'],
            'cache' => ['status' => 'success', 'text' => 'Active'],
            'storage' => ['status' => 'warning', 'text' => '75% Used'],
            'backups' => ['status' => 'success', 'text' => 'Up to date']
        ];
    }
}
