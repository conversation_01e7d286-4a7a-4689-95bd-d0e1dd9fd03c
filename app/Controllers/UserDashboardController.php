<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\BlogPostModel;
use App\Models\PageModel;
use App\Models\CommentModel;
use App\Models\MediaModel;
use CodeIgniter\HTTP\ResponseInterface;

class UserDashboardController extends BaseController
{
    protected $userModel;
    protected $blogPostModel;
    protected $pageModel;
    protected $commentModel;
    protected $mediaModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->blogPostModel = new BlogPostModel();
        $this->pageModel = new PageModel();
        $this->commentModel = new CommentModel();
        $this->mediaModel = new MediaModel();
        $this->session = session();
    }

    /**
     * User dashboard home
     */
    public function index()
    {
        if (!$this->session->get('is_logged_in')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to access your dashboard.');
        }

        $userId = $this->session->get('user_id');
        $user = $this->getUserWithFields($userId);

        if (!$user) {
            return redirect()->to('/auth/login')->with('error', 'User not found.');
        }

        // Get user statistics
        $stats = $this->getUserStats($userId);

        // Get recent activity
        $recentPosts = $this->blogPostModel->where('author_id', $userId)
                                          ->orderBy('created_at', 'DESC')
                                          ->limit(5)
                                          ->findAll();

        $recentPages = $this->pageModel->where('author_id', $userId)
                                      ->orderBy('created_at', 'DESC')
                                      ->limit(5)
                                      ->findAll();

        $data = [
            'title' => 'My Dashboard',
            'user' => $user,
            'stats' => $stats,
            'recent_posts' => $recentPosts,
            'recent_pages' => $recentPages
        ];

        return view('user/dashboard', $data);
    }

    /**
     * User profile management
     */
    public function profile()
    {
        if (!$this->session->get('is_logged_in')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to access your profile.');
        }

        $userId = $this->session->get('user_id');
        $user = $this->getUserWithFields($userId);

        if (!$user) {
            return redirect()->to('/auth/login')->with('error', 'User not found.');
        }

        $data = [
            'title' => 'My Profile',
            'user' => $user
        ];

        return view('user/profile', $data);
    }

    /**
     * Update user profile
     */
    public function updateProfile()
    {
        if (!$this->session->get('is_logged_in')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to access your profile.');
        }

        $userId = $this->session->get('user_id');
        $user = $this->getUserWithFields($userId);

        if (!$user) {
            return redirect()->to('/auth/login')->with('error', 'User not found.');
        }

        $rules = [
            'first_name' => 'required|max_length[50]',
            'last_name' => 'required|max_length[50]',
            'email' => "required|valid_email|is_unique[users.email,id,{$userId}]",
            'bio' => 'max_length[500]'
        ];

        // Only validate password if provided
        if ($this->request->getPost('password')) {
            $rules['password'] = 'min_length[6]';
            $rules['password_confirm'] = 'matches[password]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $updateData = [
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'email' => $this->request->getPost('email'),
            'bio' => $this->request->getPost('bio')
        ];

        // Only update password if provided
        if ($this->request->getPost('password')) {
            $updateData['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
        }

        if ($this->userModel->update($userId, $updateData)) {
            // Update session data
            $this->session->set([
                'first_name' => $updateData['first_name'],
                'last_name' => $updateData['last_name'],
                'email' => $updateData['email']
            ]);

            return redirect()->to('/user/profile')->with('success', 'Profile updated successfully!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update profile.');
        }
    }

    /**
     * User's blog posts
     */
    public function myPosts()
    {
        if (!$this->session->get('is_logged_in')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to access your posts.');
        }

        $userId = $this->session->get('user_id');
        $user = $this->getUserWithFields($userId);

        if (!$user) {
            return redirect()->to('/auth/login')->with('error', 'User not found.');
        }

        $posts = $this->blogPostModel->where('author_id', $userId)
                                    ->orderBy('created_at', 'DESC')
                                    ->findAll();

        $data = [
            'title' => 'My Blog Posts',
            'user' => $user,
            'posts' => $posts
        ];

        return view('user/posts', $data);
    }

    /**
     * User's pages
     */
    public function myPages()
    {
        if (!$this->session->get('is_logged_in')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to access your pages.');
        }

        $userId = $this->session->get('user_id');
        $user = $this->getUserWithFields($userId);

        if (!$user) {
            return redirect()->to('/auth/login')->with('error', 'User not found.');
        }

        $pages = $this->pageModel->where('author_id', $userId)
                                ->orderBy('created_at', 'DESC')
                                ->findAll();

        $data = [
            'title' => 'My Pages',
            'user' => $user,
            'pages' => $pages
        ];

        return view('user/pages', $data);
    }

    /**
     * User's media files
     */
    public function myMedia()
    {
        if (!$this->session->get('is_logged_in')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to access your media.');
        }

        $userId = $this->session->get('user_id');
        $user = $this->getUserWithFields($userId);

        if (!$user) {
            return redirect()->to('/auth/login')->with('error', 'User not found.');
        }

        $media = $this->mediaModel->where('uploaded_by', $userId)
                                 ->orderBy('created_at', 'DESC')
                                 ->findAll();

        $data = [
            'title' => 'My Media',
            'user' => $user,
            'media' => $media
        ];

        return view('user/media', $data);
    }

    /**
     * User settings
     */
    public function settings()
    {
        if (!$this->session->get('is_logged_in')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to access settings.');
        }

        $userId = $this->session->get('user_id');
        $user = $this->getUserWithFields($userId);

        if (!$user) {
            return redirect()->to('/auth/login')->with('error', 'User not found.');
        }

        $data = [
            'title' => 'Account Settings',
            'user' => $user
        ];

        return view('user/settings', $data);
    }

    /**
     * Get user with all required fields
     */
    private function getUserWithFields($userId)
    {
        return $this->userModel->select('id, username, email, first_name, last_name, role, status, avatar, bio, email_verified_at, last_login, created_at, updated_at')
                               ->find($userId);
    }

    /**
     * Get user statistics
     */
    private function getUserStats($userId)
    {
        $stats = [
            'total_posts' => $this->blogPostModel->where('author_id', $userId)->countAllResults(),
            'published_posts' => $this->blogPostModel->where('author_id', $userId)->where('status', 'published')->countAllResults(),
            'draft_posts' => $this->blogPostModel->where('author_id', $userId)->where('status', 'draft')->countAllResults(),
            'total_pages' => $this->pageModel->where('author_id', $userId)->countAllResults(),
            'published_pages' => $this->pageModel->where('author_id', $userId)->where('status', 'published')->countAllResults(),
            'total_media' => $this->mediaModel->where('uploaded_by', $userId)->countAllResults(),
            'total_comments' => 0 // Will be calculated based on user's email
        ];

        // Get user's email for comment count
        $user = $this->getUserWithFields($userId);
        if ($user) {
            $stats['total_comments'] = $this->commentModel->where('author_email', $user['email'])->countAllResults();
        }

        return $stats;
    }
}
