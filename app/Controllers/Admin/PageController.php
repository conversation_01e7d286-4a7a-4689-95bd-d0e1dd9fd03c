<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\PageModel;
use App\Models\UserModel;

class PageController extends BaseController
{
    protected $pageModel;
    protected $userModel;
    protected $session;

    public function __construct()
    {
        $this->pageModel = new PageModel();
        $this->userModel = new UserModel();
        $this->session = session();
    }

    /**
     * Display list of pages
     */
    public function index()
    {
        $data = [
            'title' => 'Pages',
            'pages' => $this->pageModel->getPagesWithAuthor(),
            'user' => [
                'id' => $this->session->get('user_id'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ]
        ];

        return view('admin/pages/index', $data);
    }

    /**
     * Show create page form
     */
    public function create()
    {
        $data = [
            'title' => 'Create New Page',
            'page' => null,
            'parents' => $this->getParentOptions(),
            'user' => [
                'id' => $this->session->get('user_id'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ]
        ];

        return view('admin/pages/form', $data);
    }

    /**
     * Store new page
     */
    public function store()
    {
        $rules = [
            'title' => 'required|max_length[255]',
            'content' => 'permit_empty',
            'status' => 'required|in_list[draft,published,private]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'title' => $this->request->getPost('title'),
            'slug' => $this->request->getPost('slug') ?: url_title($this->request->getPost('title'), '-', true),
            'content' => $this->request->getPost('content'),
            'excerpt' => $this->request->getPost('excerpt'),
            'meta_title' => $this->request->getPost('meta_title'),
            'meta_description' => $this->request->getPost('meta_description'),
            'meta_keywords' => $this->request->getPost('meta_keywords'),
            'featured_image' => $this->request->getPost('featured_image'),
            'template' => $this->request->getPost('template') ?: 'default',
            'status' => $this->request->getPost('status'),
            'author_id' => $this->session->get('user_id'),
            'parent_id' => $this->request->getPost('parent_id') ?: null,
            'sort_order' => $this->request->getPost('sort_order') ?: 0,
        ];

        if ($data['status'] === 'published' && !$this->request->getPost('published_at')) {
            $data['published_at'] = date('Y-m-d H:i:s');
        } elseif ($this->request->getPost('published_at')) {
            $data['published_at'] = $this->request->getPost('published_at');
        }

        if ($this->pageModel->insert($data)) {
            return redirect()->to('/admin/pages')->with('success', 'Page created successfully!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create page. Please try again.');
        }
    }

    /**
     * Show edit page form
     */
    public function edit($id)
    {
        $page = $this->pageModel->find($id);
        
        if (!$page) {
            return redirect()->to('/admin/pages')->with('error', 'Page not found.');
        }

        $data = [
            'title' => 'Edit Page',
            'page' => $page,
            'parents' => $this->getParentOptions($id),
            'user' => [
                'id' => $this->session->get('user_id'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ]
        ];

        return view('admin/pages/form', $data);
    }

    /**
     * Update page
     */
    public function update($id)
    {
        $page = $this->pageModel->find($id);
        
        if (!$page) {
            return redirect()->to('/admin/pages')->with('error', 'Page not found.');
        }

        $rules = [
            'title' => 'required|max_length[255]',
            'content' => 'permit_empty',
            'status' => 'required|in_list[draft,published,private]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'title' => $this->request->getPost('title'),
            'slug' => $this->request->getPost('slug') ?: url_title($this->request->getPost('title'), '-', true),
            'content' => $this->request->getPost('content'),
            'excerpt' => $this->request->getPost('excerpt'),
            'meta_title' => $this->request->getPost('meta_title'),
            'meta_description' => $this->request->getPost('meta_description'),
            'meta_keywords' => $this->request->getPost('meta_keywords'),
            'featured_image' => $this->request->getPost('featured_image'),
            'template' => $this->request->getPost('template') ?: 'default',
            'status' => $this->request->getPost('status'),
            'parent_id' => $this->request->getPost('parent_id') ?: null,
            'sort_order' => $this->request->getPost('sort_order') ?: 0,
        ];

        if ($data['status'] === 'published' && !$page['published_at'] && !$this->request->getPost('published_at')) {
            $data['published_at'] = date('Y-m-d H:i:s');
        } elseif ($this->request->getPost('published_at')) {
            $data['published_at'] = $this->request->getPost('published_at');
        }

        if ($this->pageModel->update($id, $data)) {
            return redirect()->to('/admin/pages')->with('success', 'Page updated successfully!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update page. Please try again.');
        }
    }

    /**
     * Delete page
     */
    public function delete($id)
    {
        $page = $this->pageModel->find($id);
        
        if (!$page) {
            return $this->response->setJSON(['success' => false, 'message' => 'Page not found.']);
        }

        // Check if page has children
        $children = $this->pageModel->getChildPages($id);
        if (!empty($children)) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot delete page with child pages.']);
        }

        if ($this->pageModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Page deleted successfully!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete page.']);
        }
    }

    /**
     * Get parent page options for dropdown
     */
    private function getParentOptions($excludeId = null)
    {
        $pages = $this->pageModel->select('id, title, parent_id')
                                ->where('status !=', 'trash')
                                ->orderBy('title', 'ASC')
                                ->findAll();

        $options = ['' => 'No Parent'];
        
        foreach ($pages as $page) {
            if ($excludeId && $page['id'] == $excludeId) {
                continue; // Don't allow page to be its own parent
            }
            $options[$page['id']] = $page['title'];
        }

        return $options;
    }

    /**
     * Preview page
     */
    public function preview($id)
    {
        $page = $this->pageModel->find($id);
        
        if (!$page) {
            return redirect()->to('/admin/pages')->with('error', 'Page not found.');
        }

        $data = [
            'page' => $page,
            'preview' => true
        ];

        return view('frontend/page', $data);
    }
}
