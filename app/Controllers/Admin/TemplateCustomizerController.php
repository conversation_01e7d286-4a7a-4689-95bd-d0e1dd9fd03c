<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\TemplateModel;
use App\Models\SettingsModel;

class TemplateCustomizerController extends BaseController
{
    protected $templateModel;
    protected $settingsModel;
    protected $session;

    public function __construct()
    {
        $this->templateModel = new TemplateModel();
        $this->settingsModel = new SettingsModel();
        $this->session = session();

        // Check if user is logged in and has admin access
        if (!$this->session->get('is_logged_in')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Access denied');
        }

        $userRole = $this->session->get('role');
        if (!in_array($userRole, ['admin', 'editor'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Access denied');
        }
    }

    /**
     * Display template customizer
     */
    public function index($templateId)
    {
        $template = $this->templateModel->find($templateId);
        
        if (!$template) {
            return redirect()->to('/admin/themes')->with('error', 'Template not found.');
        }

        // Get template customization options
        $customizations = $this->getTemplateCustomizations($template);
        
        $data = [
            'title' => 'Customize: ' . $template['name'],
            'user' => [
                'id' => $this->session->get('user_id'),
                'username' => $this->session->get('username'),
                'email' => $this->session->get('email'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ],
            'template' => $template,
            'customizations' => $customizations,
            'color_schemes' => $this->getColorSchemes(),
            'font_options' => $this->getFontOptions()
        ];

        return view('admin/templates/customizer', $data);
    }

    /**
     * Save template customizations
     */
    public function save($templateId)
    {
        $template = $this->templateModel->find($templateId);
        
        if (!$template) {
            return $this->response->setJSON(['success' => false, 'message' => 'Template not found.']);
        }

        try {
            $customizations = $this->request->getPost('customizations');
            
            // Save customizations to settings
            foreach ($customizations as $key => $value) {
                $settingKey = 'template_' . $templateId . '_' . $key;
                $this->settingsModel->setSetting($settingKey, $value);
            }

            // Generate custom CSS
            $this->generateCustomCSS($templateId, $customizations);

            return $this->response->setJSON(['success' => true, 'message' => 'Customizations saved successfully!']);

        } catch (Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to save customizations: ' . $e->getMessage()]);
        }
    }

    /**
     * Get template customization options
     */
    private function getTemplateCustomizations($template)
    {
        $templateId = $template['id'];
        
        return [
            'colors' => [
                'primary_color' => $this->settingsModel->getSetting('template_' . $templateId . '_primary_color', '#667eea'),
                'secondary_color' => $this->settingsModel->getSetting('template_' . $templateId . '_secondary_color', '#764ba2'),
                'accent_color' => $this->settingsModel->getSetting('template_' . $templateId . '_accent_color', '#28a745'),
                'text_color' => $this->settingsModel->getSetting('template_' . $templateId . '_text_color', '#333333'),
                'background_color' => $this->settingsModel->getSetting('template_' . $templateId . '_background_color', '#ffffff'),
            ],
            'typography' => [
                'heading_font' => $this->settingsModel->getSetting('template_' . $templateId . '_heading_font', 'Poppins'),
                'body_font' => $this->settingsModel->getSetting('template_' . $templateId . '_body_font', 'Open Sans'),
                'font_size' => $this->settingsModel->getSetting('template_' . $templateId . '_font_size', '16'),
                'line_height' => $this->settingsModel->getSetting('template_' . $templateId . '_line_height', '1.6'),
            ],
            'layout' => [
                'container_width' => $this->settingsModel->getSetting('template_' . $templateId . '_container_width', '1200'),
                'header_style' => $this->settingsModel->getSetting('template_' . $templateId . '_header_style', 'default'),
                'footer_style' => $this->settingsModel->getSetting('template_' . $templateId . '_footer_style', 'default'),
                'sidebar_position' => $this->settingsModel->getSetting('template_' . $templateId . '_sidebar_position', 'right'),
            ],
            'features' => [
                'show_breadcrumbs' => $this->settingsModel->getSetting('template_' . $templateId . '_show_breadcrumbs', true),
                'enable_animations' => $this->settingsModel->getSetting('template_' . $templateId . '_enable_animations', true),
                'show_social_links' => $this->settingsModel->getSetting('template_' . $templateId . '_show_social_links', true),
                'enable_dark_mode' => $this->settingsModel->getSetting('template_' . $templateId . '_enable_dark_mode', false),
            ]
        ];
    }

    /**
     * Get predefined color schemes
     */
    private function getColorSchemes()
    {
        return [
            'default' => [
                'name' => 'Default Blue',
                'primary' => '#667eea',
                'secondary' => '#764ba2',
                'accent' => '#28a745'
            ],
            'purple' => [
                'name' => 'Purple Dream',
                'primary' => '#8e44ad',
                'secondary' => '#9b59b6',
                'accent' => '#e74c3c'
            ],
            'green' => [
                'name' => 'Nature Green',
                'primary' => '#27ae60',
                'secondary' => '#2ecc71',
                'accent' => '#f39c12'
            ],
            'orange' => [
                'name' => 'Sunset Orange',
                'primary' => '#e67e22',
                'secondary' => '#f39c12',
                'accent' => '#e74c3c'
            ],
            'dark' => [
                'name' => 'Dark Mode',
                'primary' => '#2c3e50',
                'secondary' => '#34495e',
                'accent' => '#3498db'
            ]
        ];
    }

    /**
     * Get font options
     */
    private function getFontOptions()
    {
        return [
            'Poppins' => 'Poppins',
            'Open Sans' => 'Open Sans',
            'Roboto' => 'Roboto',
            'Lato' => 'Lato',
            'Montserrat' => 'Montserrat',
            'Source Sans Pro' => 'Source Sans Pro',
            'Raleway' => 'Raleway',
            'Ubuntu' => 'Ubuntu',
            'Nunito' => 'Nunito',
            'Inter' => 'Inter'
        ];
    }

    /**
     * Generate custom CSS file
     */
    private function generateCustomCSS($templateId, $customizations)
    {
        $template = $this->templateModel->find($templateId);
        $cssDir = FCPATH . 'assets/templates/' . $template['template_id'] . '/css/';
        
        if (!is_dir($cssDir)) {
            mkdir($cssDir, 0755, true);
        }

        $css = "/* Custom CSS for {$template['name']} */\n\n";
        
        // Colors
        if (isset($customizations['colors'])) {
            $css .= ":root {\n";
            foreach ($customizations['colors'] as $key => $value) {
                $cssVar = str_replace('_', '-', $key);
                $css .= "  --{$cssVar}: {$value};\n";
            }
            $css .= "}\n\n";
            
            $css .= "/* Color Applications */\n";
            $css .= ".btn-primary, .bg-primary { background-color: var(--primary-color) !important; }\n";
            $css .= ".btn-secondary, .bg-secondary { background-color: var(--secondary-color) !important; }\n";
            $css .= ".text-primary { color: var(--primary-color) !important; }\n";
            $css .= "body { color: var(--text-color); background-color: var(--background-color); }\n\n";
        }

        // Typography
        if (isset($customizations['typography'])) {
            $css .= "/* Typography */\n";
            if (!empty($customizations['typography']['heading_font'])) {
                $css .= "h1, h2, h3, h4, h5, h6 { font-family: '{$customizations['typography']['heading_font']}', sans-serif; }\n";
            }
            if (!empty($customizations['typography']['body_font'])) {
                $css .= "body, p, span, div { font-family: '{$customizations['typography']['body_font']}', sans-serif; }\n";
            }
            if (!empty($customizations['typography']['font_size'])) {
                $css .= "body { font-size: {$customizations['typography']['font_size']}px; }\n";
            }
            if (!empty($customizations['typography']['line_height'])) {
                $css .= "body { line-height: {$customizations['typography']['line_height']}; }\n";
            }
            $css .= "\n";
        }

        // Layout
        if (isset($customizations['layout'])) {
            $css .= "/* Layout */\n";
            if (!empty($customizations['layout']['container_width'])) {
                $css .= ".container { max-width: {$customizations['layout']['container_width']}px; }\n";
            }
            $css .= "\n";
        }

        // Features
        if (isset($customizations['features'])) {
            $css .= "/* Features */\n";
            if (empty($customizations['features']['show_breadcrumbs'])) {
                $css .= ".breadcrumb { display: none !important; }\n";
            }
            if (empty($customizations['features']['enable_animations'])) {
                $css .= "* { transition: none !important; animation: none !important; }\n";
            }
            if (empty($customizations['features']['show_social_links'])) {
                $css .= ".social-links { display: none !important; }\n";
            }
            $css .= "\n";
        }

        // Save CSS file
        file_put_contents($cssDir . 'custom.css', $css);
    }

    /**
     * Reset template customizations
     */
    public function reset($templateId)
    {
        try {
            $template = $this->templateModel->find($templateId);
            
            if (!$template) {
                return $this->response->setJSON(['success' => false, 'message' => 'Template not found.']);
            }

            // Remove all customization settings
            $settings = $this->settingsModel->where('setting_key LIKE', 'template_' . $templateId . '_%')->findAll();
            foreach ($settings as $setting) {
                $this->settingsModel->delete($setting['id']);
            }

            // Remove custom CSS file
            $cssFile = FCPATH . 'assets/templates/' . $template['template_id'] . '/css/custom.css';
            if (file_exists($cssFile)) {
                unlink($cssFile);
            }

            return $this->response->setJSON(['success' => true, 'message' => 'Template reset to defaults successfully!']);

        } catch (Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to reset template: ' . $e->getMessage()]);
        }
    }

    /**
     * Export template customizations
     */
    public function export($templateId)
    {
        $template = $this->templateModel->find($templateId);
        
        if (!$template) {
            return redirect()->to('/admin/themes')->with('error', 'Template not found.');
        }

        $customizations = $this->getTemplateCustomizations($template);
        
        $export = [
            'template_name' => $template['name'],
            'template_id' => $template['template_id'],
            'export_date' => date('Y-m-d H:i:s'),
            'customizations' => $customizations
        ];

        $filename = 'template_' . $template['template_id'] . '_customizations_' . date('Y-m-d') . '.json';
        
        return $this->response
            ->setHeader('Content-Type', 'application/json')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody(json_encode($export, JSON_PRETTY_PRINT));
    }

    /**
     * Import template customizations
     */
    public function import($templateId)
    {
        try {
            $file = $this->request->getFile('customizations_file');
            
            if (!$file || !$file->isValid()) {
                return $this->response->setJSON(['success' => false, 'message' => 'Please select a valid customizations file.']);
            }

            $content = file_get_contents($file->getTempName());
            $data = json_decode($content, true);

            if (!$data || !isset($data['customizations'])) {
                return $this->response->setJSON(['success' => false, 'message' => 'Invalid customizations file format.']);
            }

            // Apply customizations
            foreach ($data['customizations'] as $section => $settings) {
                foreach ($settings as $key => $value) {
                    $settingKey = 'template_' . $templateId . '_' . $key;
                    $this->settingsModel->setSetting($settingKey, $value);
                }
            }

            // Generate custom CSS
            $this->generateCustomCSS($templateId, $data['customizations']);

            return $this->response->setJSON(['success' => true, 'message' => 'Customizations imported successfully!']);

        } catch (Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to import customizations: ' . $e->getMessage()]);
        }
    }
}
