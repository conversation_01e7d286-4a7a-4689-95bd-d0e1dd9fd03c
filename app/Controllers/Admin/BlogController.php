<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\BlogPostModel;
use App\Models\CategoryModel;
use App\Models\TagModel;
use App\Models\CommentModel;
use CodeIgniter\HTTP\ResponseInterface;

class BlogController extends BaseController
{
    protected $blogPostModel;
    protected $categoryModel;
    protected $tagModel;
    protected $commentModel;

    public function __construct()
    {
        $this->blogPostModel = new BlogPostModel();
        $this->categoryModel = new CategoryModel();
        $this->tagModel = new TagModel();
        $this->commentModel = new CommentModel();

        // Check if user is logged in and has blog access
        if (!session()->get('is_logged_in')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Access denied');
        }

        $userRole = session()->get('role');
        if (!in_array($userRole, ['admin', 'editor', 'author'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Access denied');
        }
    }

    /**
     * Display blog posts list
     */
    public function index()
    {
        $data = [
            'title' => 'Blog Posts',
            'user' => [
                'id' => session()->get('user_id'),
                'username' => session()->get('username'),
                'email' => session()->get('email'),
                'role' => session()->get('role'),
                'first_name' => session()->get('first_name'),
                'last_name' => session()->get('last_name'),
            ],
            'posts' => $this->blogPostModel->getPostsWithDetails(),
            'categories' => $this->categoryModel->getActiveCategories(),
            'tags' => $this->tagModel->getActiveTags(),
        ];

        return view('admin/blog/index', $data);
    }

    /**
     * Show create post form
     */
    public function create()
    {
        $data = [
            'title' => 'Create New Post',
            'user' => [
                'id' => session()->get('user_id'),
                'username' => session()->get('username'),
                'email' => session()->get('email'),
                'role' => session()->get('role'),
                'first_name' => session()->get('first_name'),
                'last_name' => session()->get('last_name'),
            ],
            'categories' => $this->categoryModel->getActiveCategories(),
            'tags' => $this->tagModel->getActiveTags(),
            'post' => null,
        ];

        return view('admin/blog/form', $data);
    }

    /**
     * Store new blog post
     */
    public function store()
    {
        $rules = [
            'title' => 'required|max_length[255]',
            'content' => 'required',
            'status' => 'required|in_list[draft,published,private,scheduled]',
            'category_id' => 'permit_empty|integer',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $postData = [
            'title' => $this->request->getPost('title'),
            'slug' => $this->request->getPost('slug') ?: url_title($this->request->getPost('title'), '-', true),
            'content' => $this->request->getPost('content'),
            'excerpt' => $this->request->getPost('excerpt'),
            'featured_image' => $this->request->getPost('featured_image'),
            'meta_title' => $this->request->getPost('meta_title'),
            'meta_description' => $this->request->getPost('meta_description'),
            'meta_keywords' => $this->request->getPost('meta_keywords'),
            'status' => $this->request->getPost('status'),
            'author_id' => session()->get('user_id'),
            'category_id' => $this->request->getPost('category_id') ?: null,
        ];

        // Set published_at for published posts
        if ($postData['status'] === 'published') {
            $postData['published_at'] = date('Y-m-d H:i:s');
        } elseif ($postData['status'] === 'scheduled') {
            $publishedAt = $this->request->getPost('published_at');
            $postData['published_at'] = $publishedAt ?: date('Y-m-d H:i:s');
        }

        try {
            $postId = $this->blogPostModel->insert($postData);

            if ($postId) {
                // Handle tags
                $this->handlePostTags($postId, $this->request->getPost('tags'));

                return redirect()->to('/admin/blog')->with('success', 'Blog post created successfully!');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create blog post.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Show edit post form
     */
    public function edit($id)
    {
        $post = $this->blogPostModel->find($id);

        if (!$post) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Post not found');
        }

        // Get post tags
        $postTags = $this->tagModel->getPostTags($id);
        $post['tags'] = array_column($postTags, 'name');

        $data = [
            'title' => 'Edit Post',
            'user' => [
                'id' => session()->get('user_id'),
                'username' => session()->get('username'),
                'email' => session()->get('email'),
                'role' => session()->get('role'),
                'first_name' => session()->get('first_name'),
                'last_name' => session()->get('last_name'),
            ],
            'post' => $post,
            'categories' => $this->categoryModel->getActiveCategories(),
            'tags' => $this->tagModel->getActiveTags(),
        ];

        return view('admin/blog/form', $data);
    }

    /**
     * Update blog post
     */
    public function update($id)
    {
        $post = $this->blogPostModel->find($id);

        if (!$post) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Post not found');
        }

        $rules = [
            'title' => 'required|max_length[255]',
            'content' => 'required',
            'status' => 'required|in_list[draft,published,private,scheduled]',
            'category_id' => 'permit_empty|integer',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $postData = [
            'title' => $this->request->getPost('title'),
            'slug' => $this->request->getPost('slug') ?: url_title($this->request->getPost('title'), '-', true),
            'content' => $this->request->getPost('content'),
            'excerpt' => $this->request->getPost('excerpt'),
            'featured_image' => $this->request->getPost('featured_image'),
            'meta_title' => $this->request->getPost('meta_title'),
            'meta_description' => $this->request->getPost('meta_description'),
            'meta_keywords' => $this->request->getPost('meta_keywords'),
            'status' => $this->request->getPost('status'),
            'category_id' => $this->request->getPost('category_id') ?: null,
        ];

        // Handle published_at for status changes
        if ($postData['status'] === 'published' && $post['status'] !== 'published') {
            $postData['published_at'] = date('Y-m-d H:i:s');
        } elseif ($postData['status'] === 'scheduled') {
            $publishedAt = $this->request->getPost('published_at');
            $postData['published_at'] = $publishedAt ?: date('Y-m-d H:i:s');
        }

        try {
            $this->blogPostModel->update($id, $postData);

            // Handle tags
            $this->handlePostTags($id, $this->request->getPost('tags'));

            return redirect()->to('/admin/blog')->with('success', 'Blog post updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Delete blog post
     */
    public function delete($id)
    {
        $post = $this->blogPostModel->find($id);

        if (!$post) {
            return $this->response->setJSON(['success' => false, 'message' => 'Post not found']);
        }

        try {
            // Delete post tags first
            db_connect()->table('post_tags')->where('post_id', $id)->delete();

            // Delete the post
            $this->blogPostModel->delete($id);

            return $this->response->setJSON(['success' => true, 'message' => 'Post deleted successfully']);
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Duplicate blog post
     */
    public function duplicate($id)
    {
        $post = $this->blogPostModel->find($id);

        if (!$post) {
            return $this->response->setJSON(['success' => false, 'message' => 'Post not found']);
        }

        try {
            // Prepare duplicate data
            unset($post['id']);
            $post['title'] = $post['title'] . ' (Copy)';
            $post['slug'] = $post['slug'] . '-copy-' . time();
            $post['status'] = 'draft';
            $post['published_at'] = null;
            $post['view_count'] = 0;
            $post['comment_count'] = 0;
            $post['created_at'] = null;
            $post['updated_at'] = null;

            $newPostId = $this->blogPostModel->insert($post);

            if ($newPostId) {
                // Copy tags
                $originalTags = $this->tagModel->getPostTags($id);
                if (!empty($originalTags)) {
                    $tagNames = array_column($originalTags, 'name');
                    $this->handlePostTags($newPostId, implode(',', $tagNames));
                }

                return $this->response->setJSON(['success' => true, 'message' => 'Post duplicated successfully']);
            } else {
                return $this->response->setJSON(['success' => false, 'message' => 'Failed to duplicate post']);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Handle post tags
     */
    private function handlePostTags($postId, $tagsString)
    {
        // Delete existing tags for this post
        db_connect()->table('post_tags')->where('post_id', $postId)->delete();

        if (empty($tagsString)) {
            return;
        }

        $tagNames = array_map('trim', explode(',', $tagsString));
        $tagNames = array_filter($tagNames); // Remove empty values

        foreach ($tagNames as $tagName) {
            if (empty($tagName)) continue;

            // Create or get existing tag
            $tag = $this->tagModel->createOrGetTag($tagName);

            if ($tag) {
                // Insert post-tag relationship
                db_connect()->table('post_tags')->insert([
                    'post_id' => $postId,
                    'tag_id' => $tag['id'],
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    /**
     * Get posts by status (AJAX)
     */
    public function getPostsByStatus()
    {
        $status = $this->request->getGet('status');

        if ($status === 'all') {
            $posts = $this->blogPostModel->getPostsWithDetails();
        } else {
            $posts = $this->blogPostModel->where('status', $status)->findAll();
        }

        return $this->response->setJSON($posts);
    }

    /**
     * Bulk actions for posts
     */
    public function bulkAction()
    {
        $action = $this->request->getPost('action');
        $postIds = $this->request->getPost('post_ids');

        if (empty($action) || empty($postIds)) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        try {
            switch ($action) {
                case 'delete':
                    foreach ($postIds as $postId) {
                        db_connect()->table('post_tags')->where('post_id', $postId)->delete();
                        $this->blogPostModel->delete($postId);
                    }
                    $message = 'Posts deleted successfully';
                    break;

                case 'publish':
                    $this->blogPostModel->whereIn('id', $postIds)->set([
                        'status' => 'published',
                        'published_at' => date('Y-m-d H:i:s')
                    ])->update();
                    $message = 'Posts published successfully';
                    break;

                case 'draft':
                    $this->blogPostModel->whereIn('id', $postIds)->set([
                        'status' => 'draft',
                        'published_at' => null
                    ])->update();
                    $message = 'Posts moved to draft successfully';
                    break;

                default:
                    return $this->response->setJSON(['success' => false, 'message' => 'Invalid action']);
            }

            return $this->response->setJSON(['success' => true, 'message' => $message]);
        } catch (\Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Search posts (AJAX)
     */
    public function search()
    {
        $query = $this->request->getGet('q');

        if (empty($query)) {
            return $this->response->setJSON([]);
        }

        $posts = $this->blogPostModel->searchPosts($query, 10);

        return $this->response->setJSON($posts);
    }
}
