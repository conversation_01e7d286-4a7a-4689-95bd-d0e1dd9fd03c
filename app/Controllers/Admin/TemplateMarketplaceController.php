<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\TemplateModel;
use App\Models\SettingsModel;

class TemplateMarketplaceController extends BaseController
{
    protected $templateModel;
    protected $settingsModel;
    protected $session;

    public function __construct()
    {
        $this->templateModel = new TemplateModel();
        $this->settingsModel = new SettingsModel();
        $this->session = session();

        // Check if user is logged in and has admin access
        if (!$this->session->get('is_logged_in')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Access denied');
        }

        $userRole = $this->session->get('role');
        if (!in_array($userRole, ['admin', 'editor'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Access denied');
        }
    }

    /**
     * Display TemplateMonster marketplace
     */
    public function index()
    {
        $data = [
            'title' => 'Template Marketplace',
            'user' => [
                'id' => $this->session->get('user_id'),
                'username' => $this->session->get('username'),
                'email' => $this->session->get('email'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ],
            'templates' => $this->getTemplateMonsterTemplates(),
            'installed_templates' => $this->templateModel->findAll()
        ];

        return view('admin/templates/marketplace', $data);
    }

    /**
     * Get popular TemplateMonster templates
     */
    private function getTemplateMonsterTemplates()
    {
        return [
            [
                'id' => 'tm-591',
                'name' => 'Villa Agency',
                'description' => 'Modern real estate and property website template with Bootstrap 5',
                'category' => 'Business',
                'preview_url' => 'https://templatemo.com/tm-591-villa-agency',
                'download_url' => 'https://templatemo.com/download/tm-591-villa-agency',
                'thumbnail' => 'https://templatemo.com/templates/tm_591_villa_agency/preview.jpg',
                'features' => ['Bootstrap 5', 'Responsive', 'Modern Design', 'Real Estate'],
                'pages' => ['Home', 'Properties', 'Property Details', 'Contact'],
                'rating' => 4.8,
                'downloads' => '15.2K'
            ],
            [
                'id' => 'tm-558',
                'name' => 'Klassy Cafe',
                'description' => 'Restaurant and cafe website template with elegant design',
                'category' => 'Restaurant',
                'preview_url' => 'https://templatemo.com/tm-558-klassy-cafe',
                'download_url' => 'https://templatemo.com/download/tm-558-klassy-cafe',
                'thumbnail' => 'https://templatemo.com/templates/tm_558_klassy_cafe/preview.jpg',
                'features' => ['One Page', 'Gallery', 'Menu Display', 'Contact Form'],
                'pages' => ['Home', 'About', 'Menu', 'Chefs', 'Contact'],
                'rating' => 4.7,
                'downloads' => '12.8K'
            ],
            [
                'id' => 'tm-515',
                'name' => 'Eatery',
                'description' => 'Food and restaurant template with portfolio gallery',
                'category' => 'Restaurant',
                'preview_url' => 'https://templatemo.com/tm-515-eatery',
                'download_url' => 'https://templatemo.com/download/tm-515-eatery',
                'thumbnail' => 'https://templatemo.com/templates/tm_515_eatery/preview.jpg',
                'features' => ['Portfolio Gallery', 'Pop-up Images', 'Contact Form', 'Responsive'],
                'pages' => ['Home', 'About', 'Services', 'Gallery', 'Contact'],
                'rating' => 4.6,
                'downloads' => '9.5K'
            ],
            [
                'id' => 'tm-493',
                'name' => 'Snapshot',
                'description' => 'Photography and portfolio template with Bootstrap',
                'category' => 'Portfolio',
                'preview_url' => 'https://templatemo.com/tm-493-snapshot',
                'download_url' => 'https://templatemo.com/download/tm-493-snapshot',
                'thumbnail' => 'https://templatemo.com/templates/tm_493_snapshot/preview.jpg',
                'features' => ['Bootstrap 3', 'Portfolio Grid', 'Lightbox', 'Responsive'],
                'pages' => ['Home', 'About', 'Gallery', 'Services', 'Contact'],
                'rating' => 4.5,
                'downloads' => '18.3K'
            ],
            [
                'id' => 'tm-392',
                'name' => 'Avocado',
                'description' => 'Multi-page business template with blog functionality',
                'category' => 'Business',
                'preview_url' => 'https://templatemo.com/tm-392-avocado',
                'download_url' => 'https://templatemo.com/download/tm-392-avocado',
                'thumbnail' => 'https://templatemo.com/templates/tm_392_avocado/preview.jpg',
                'features' => ['Multi-page', 'Blog Posts', 'Gallery', 'Contact Form'],
                'pages' => ['Home', 'About', 'Services', 'Gallery', 'Blog', 'Contact'],
                'rating' => 4.4,
                'downloads' => '22.1K'
            ],
            [
                'id' => 'tm-269',
                'name' => 'Pineapple',
                'description' => 'Clean and simple business template',
                'category' => 'Business',
                'preview_url' => 'https://templatemo.com/tm-269-pineapple',
                'download_url' => 'https://templatemo.com/download/tm-269-pineapple',
                'thumbnail' => 'https://templatemo.com/templates/tm_269_pineapple/preview.jpg',
                'features' => ['Clean Design', 'Simple Layout', 'Contact Form', 'Lightweight'],
                'pages' => ['Home', 'About', 'Services', 'Portfolio', 'Contact'],
                'rating' => 4.3,
                'downloads' => '31.7K'
            ],
            [
                'id' => 'tm-590',
                'name' => 'Chain App Dev',
                'description' => 'Modern app development and software company template',
                'category' => 'Technology',
                'preview_url' => 'https://templatemo.com/tm-590-chain-app-dev',
                'download_url' => 'https://templatemo.com/download/tm-590-chain-app-dev',
                'thumbnail' => 'https://templatemo.com/templates/tm_590_chain_app_dev/preview.jpg',
                'features' => ['Bootstrap 5', 'App Showcase', 'Modern Design', 'Responsive'],
                'pages' => ['Home', 'About', 'Services', 'Testimonials', 'Contact'],
                'rating' => 4.9,
                'downloads' => '8.2K'
            ],
            [
                'id' => 'tm-589',
                'name' => 'Lugx Gaming',
                'description' => 'Gaming and esports website template',
                'category' => 'Gaming',
                'preview_url' => 'https://templatemo.com/tm-589-lugx-gaming',
                'download_url' => 'https://templatemo.com/download/tm-589-lugx-gaming',
                'thumbnail' => 'https://templatemo.com/templates/tm_589_lugx_gaming/preview.jpg',
                'features' => ['Gaming Theme', 'Dark Design', 'Product Showcase', 'Modern'],
                'pages' => ['Home', 'Shop', 'Product Details', 'Contact'],
                'rating' => 4.7,
                'downloads' => '6.8K'
            ]
        ];
    }

    /**
     * Preview template
     */
    public function preview($templateId)
    {
        $templates = $this->getTemplateMonsterTemplates();
        $template = null;
        
        foreach ($templates as $tmpl) {
            if ($tmpl['id'] === $templateId) {
                $template = $tmpl;
                break;
            }
        }
        
        if (!$template) {
            return redirect()->to('/admin/templates/marketplace')->with('error', 'Template not found.');
        }

        $data = [
            'title' => 'Preview: ' . $template['name'],
            'user' => [
                'id' => $this->session->get('user_id'),
                'username' => $this->session->get('username'),
                'email' => $this->session->get('email'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ],
            'template' => $template
        ];

        return view('admin/templates/preview', $data);
    }

    /**
     * Install template
     */
    public function install($templateId)
    {
        $templates = $this->getTemplateMonsterTemplates();
        $template = null;
        
        foreach ($templates as $tmpl) {
            if ($tmpl['id'] === $templateId) {
                $template = $tmpl;
                break;
            }
        }
        
        if (!$template) {
            return $this->response->setJSON(['success' => false, 'message' => 'Template not found.']);
        }

        try {
            // Create template record in database
            $templateData = [
                'name' => $template['name'],
                'type' => 'layout',
                'file_path' => 'templates/templatemo/' . $template['id'] . '/index.php',
                'description' => $template['description'],
                'status' => 'active',
                'created_by' => $this->session->get('user_id'),
                'preview_image' => $template['thumbnail'],
                'template_source' => 'templatemo',
                'template_id' => $template['id']
            ];

            // Check if already installed
            $existing = $this->templateModel->where('template_id', $template['id'])->first();
            if ($existing) {
                return $this->response->setJSON(['success' => false, 'message' => 'Template already installed.']);
            }

            // Install template files (this would download and convert the template)
            $this->installTemplateFiles($template);

            // Save to database
            if ($this->templateModel->insert($templateData)) {
                return $this->response->setJSON(['success' => true, 'message' => 'Template installed successfully!']);
            } else {
                return $this->response->setJSON(['success' => false, 'message' => 'Failed to save template to database.']);
            }

        } catch (Exception $e) {
            return $this->response->setJSON(['success' => false, 'message' => 'Installation failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Install template files (placeholder for actual implementation)
     */
    private function installTemplateFiles($template)
    {
        // Create template directory
        $templateDir = APPPATH . 'Views/templates/templatemo/' . $template['id'];
        if (!is_dir($templateDir)) {
            mkdir($templateDir, 0755, true);
        }

        // Create a basic CodeIgniter view file based on the template
        $viewContent = $this->generateCodeIgniterView($template);
        file_put_contents($templateDir . '/index.php', $viewContent);

        // Copy assets (CSS, JS, images) - this would be implemented to download from TemplateMonster
        $this->copyTemplateAssets($template);
    }

    /**
     * Generate CodeIgniter view from template
     */
    private function generateCodeIgniterView($template)
    {
        return '<?= $this->extend(\'layouts/templatemo\') ?>

<?= $this->section(\'title\') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section(\'meta\') ?>
<meta name="description" content="<?= esc($meta_description ?? \'\') ?>">
<meta name="keywords" content="<?= esc($meta_keywords ?? \'\') ?>">
<?= $this->endSection() ?>

<?= $this->section(\'styles\') ?>
<link rel="stylesheet" href="<?= base_url(\'assets/templates/' . $template['id'] . '/css/bootstrap.min.css\') ?>">
<link rel="stylesheet" href="<?= base_url(\'assets/templates/' . $template['id'] . '/css/templatemo-style.css\') ?>">
<?= $this->endSection() ?>

<?= $this->section(\'content\') ?>
<!-- ' . $template['name'] . ' Template Content -->
<div class="template-content">
    <?= $content ?>
</div>
<?= $this->endSection() ?>

<?= $this->section(\'scripts\') ?>
<script src="<?= base_url(\'assets/templates/' . $template['id'] . '/js/jquery.min.js\') ?>"></script>
<script src="<?= base_url(\'assets/templates/' . $template['id'] . '/js/bootstrap.min.js\') ?>"></script>
<script src="<?= base_url(\'assets/templates/' . $template['id'] . '/js/templatemo-script.js\') ?>"></script>
<?= $this->endSection() ?>';
    }

    /**
     * Copy template assets (placeholder)
     */
    private function copyTemplateAssets($template)
    {
        // Create assets directory
        $assetsDir = FCPATH . 'assets/templates/' . $template['id'];
        if (!is_dir($assetsDir)) {
            mkdir($assetsDir, 0755, true);
            mkdir($assetsDir . '/css', 0755, true);
            mkdir($assetsDir . '/js', 0755, true);
            mkdir($assetsDir . '/images', 0755, true);
        }

        // This would download and copy actual template assets
        // For now, create placeholder files
        file_put_contents($assetsDir . '/css/templatemo-style.css', '/* ' . $template['name'] . ' styles */');
        file_put_contents($assetsDir . '/js/templatemo-script.js', '/* ' . $template['name'] . ' scripts */');
    }
}
