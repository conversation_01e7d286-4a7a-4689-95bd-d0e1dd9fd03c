<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\MenuItemModel;
use App\Models\MenuModel;

class MenuItemController extends BaseController
{
    protected $menuItemModel;
    protected $menuModel;
    protected $session;

    public function __construct()
    {
        $this->menuItemModel = new MenuItemModel();
        $this->menuModel = new MenuModel();
        $this->session = session();
    }

    /**
     * Store new menu item
     */
    public function store()
    {
        $rules = [
            'menu_id' => 'required|integer',
            'title' => 'required|max_length[255]',
            'type' => 'required|in_list[page,custom,category]',
            'status' => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false, 
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $data = [
            'menu_id' => $this->request->getPost('menu_id'),
            'parent_id' => $this->request->getPost('parent_id') ?: null,
            'title' => $this->request->getPost('title'),
            'url' => $this->request->getPost('url'),
            'page_id' => $this->request->getPost('page_id') ?: null,
            'type' => $this->request->getPost('type'),
            'target' => $this->request->getPost('target') ?: '_self',
            'css_class' => $this->request->getPost('css_class'),
            'icon' => $this->request->getPost('icon'),
            'sort_order' => $this->request->getPost('sort_order') ?: 0,
            'status' => $this->request->getPost('status'),
        ];

        if ($this->menuItemModel->insert($data)) {
            return $this->response->setJSON([
                'success' => true, 
                'message' => 'Menu item created successfully!',
                'item_id' => $this->menuItemModel->getInsertID()
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false, 
                'message' => 'Failed to create menu item.'
            ]);
        }
    }

    /**
     * Update menu item
     */
    public function update($id)
    {
        $item = $this->menuItemModel->find($id);
        
        if (!$item) {
            return $this->response->setJSON(['success' => false, 'message' => 'Menu item not found.']);
        }

        $rules = [
            'title' => 'required|max_length[255]',
            'type' => 'required|in_list[page,custom,category]',
            'status' => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false, 
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $data = [
            'parent_id' => $this->request->getPost('parent_id') ?: null,
            'title' => $this->request->getPost('title'),
            'url' => $this->request->getPost('url'),
            'page_id' => $this->request->getPost('page_id') ?: null,
            'type' => $this->request->getPost('type'),
            'target' => $this->request->getPost('target') ?: '_self',
            'css_class' => $this->request->getPost('css_class'),
            'icon' => $this->request->getPost('icon'),
            'sort_order' => $this->request->getPost('sort_order') ?: 0,
            'status' => $this->request->getPost('status'),
        ];

        if ($this->menuItemModel->update($id, $data)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Menu item updated successfully!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to update menu item.']);
        }
    }

    /**
     * Delete menu item
     */
    public function delete($id)
    {
        $item = $this->menuItemModel->find($id);
        
        if (!$item) {
            return $this->response->setJSON(['success' => false, 'message' => 'Menu item not found.']);
        }

        if ($this->menuItemModel->deleteWithChildren($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Menu item deleted successfully!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete menu item.']);
        }
    }

    /**
     * Get menu item details
     */
    public function get($id)
    {
        $item = $this->menuItemModel->getMenuItemWithPage($id);
        
        if (!$item) {
            return $this->response->setJSON(['success' => false, 'message' => 'Menu item not found.']);
        }

        return $this->response->setJSON(['success' => true, 'item' => $item]);
    }

    /**
     * Update menu items order (drag and drop)
     */
    public function updateOrder()
    {
        $menuItems = $this->request->getJSON(true);
        
        if (!$menuItems) {
            return $this->response->setJSON(['success' => false, 'message' => 'No data received.']);
        }

        if ($this->menuItemModel->updateMenuOrder($menuItems)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Menu order updated successfully!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to update menu order.']);
        }
    }

    /**
     * Get menu items for a specific menu (AJAX)
     */
    public function getMenuItems($menuId)
    {
        $items = $this->menuItemModel->getAdminMenuItems($menuId);
        return $this->response->setJSON(['success' => true, 'items' => $items]);
    }

    /**
     * Bulk update menu items status
     */
    public function bulkUpdateStatus()
    {
        $itemIds = $this->request->getPost('item_ids');
        $status = $this->request->getPost('status');
        
        if (!$itemIds || !in_array($status, ['active', 'inactive'])) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid data.']);
        }

        $updated = 0;
        foreach ($itemIds as $id) {
            if ($this->menuItemModel->update($id, ['status' => $status])) {
                $updated++;
            }
        }

        if ($updated > 0) {
            return $this->response->setJSON([
                'success' => true, 
                'message' => "Updated {$updated} menu items successfully!"
            ]);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to update menu items.']);
        }
    }

    /**
     * Search pages for menu item creation
     */
    public function searchPages()
    {
        $query = $this->request->getGet('q');
        
        if (!$query || strlen($query) < 2) {
            return $this->response->setJSON(['success' => false, 'message' => 'Query too short.']);
        }

        $pageModel = new \App\Models\PageModel();
        $pages = $pageModel->select('id, title, slug')
                          ->like('title', $query)
                          ->where('status', 'published')
                          ->limit(10)
                          ->findAll();

        return $this->response->setJSON(['success' => true, 'pages' => $pages]);
    }
}
