<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\MenuModel;
use App\Models\MenuItemModel;
use App\Models\PageModel;

class MenuController extends BaseController
{
    protected $menuModel;
    protected $menuItemModel;
    protected $pageModel;
    protected $session;

    public function __construct()
    {
        $this->menuModel = new MenuModel();
        $this->menuItemModel = new MenuItemModel();
        $this->pageModel = new PageModel();
        $this->session = session();
    }

    /**
     * Display list of menus
     */
    public function index()
    {
        $data = [
            'title' => 'Menus',
            'menus' => $this->menuModel->getMenusWithCreator(),
            'user' => [
                'id' => $this->session->get('user_id'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ]
        ];

        return view('admin/menus/index', $data);
    }

    /**
     * Show create menu form
     */
    public function create()
    {
        $data = [
            'title' => 'Create New Menu',
            'menu' => null,
            'user' => [
                'id' => $this->session->get('user_id'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ]
        ];

        return view('admin/menus/form', $data);
    }

    /**
     * Store new menu
     */
    public function store()
    {
        $rules = [
            'name' => 'required|max_length[255]',
            'location' => 'required|in_list[primary,secondary,footer,sidebar]',
            'status' => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'slug' => $this->request->getPost('slug') ?: url_title($this->request->getPost('name'), '-', true),
            'description' => $this->request->getPost('description'),
            'location' => $this->request->getPost('location'),
            'status' => $this->request->getPost('status'),
            'created_by' => $this->session->get('user_id'),
        ];

        if ($this->menuModel->insert($data)) {
            return redirect()->to('/admin/menus')->with('success', 'Menu created successfully!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create menu. Please try again.');
        }
    }

    /**
     * Show edit menu form
     */
    public function edit($id)
    {
        $menu = $this->menuModel->find($id);
        
        if (!$menu) {
            return redirect()->to('/admin/menus')->with('error', 'Menu not found.');
        }

        $data = [
            'title' => 'Edit Menu',
            'menu' => $menu,
            'user' => [
                'id' => $this->session->get('user_id'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ]
        ];

        return view('admin/menus/form', $data);
    }

    /**
     * Update menu
     */
    public function update($id)
    {
        $menu = $this->menuModel->find($id);
        
        if (!$menu) {
            return redirect()->to('/admin/menus')->with('error', 'Menu not found.');
        }

        $rules = [
            'name' => 'required|max_length[255]',
            'location' => 'required|in_list[primary,secondary,footer,sidebar]',
            'status' => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'slug' => $this->request->getPost('slug') ?: url_title($this->request->getPost('name'), '-', true),
            'description' => $this->request->getPost('description'),
            'location' => $this->request->getPost('location'),
            'status' => $this->request->getPost('status'),
        ];

        if ($this->menuModel->update($id, $data)) {
            return redirect()->to('/admin/menus')->with('success', 'Menu updated successfully!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update menu. Please try again.');
        }
    }

    /**
     * Delete menu
     */
    public function delete($id)
    {
        $menu = $this->menuModel->find($id);
        
        if (!$menu) {
            return $this->response->setJSON(['success' => false, 'message' => 'Menu not found.']);
        }

        // Check if menu has items
        $items = $this->menuItemModel->where('menu_id', $id)->findAll();
        if (!empty($items)) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot delete menu with items. Please remove all items first.']);
        }

        if ($this->menuModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Menu deleted successfully!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete menu.']);
        }
    }

    /**
     * Menu builder interface
     */
    public function builder($id)
    {
        $menu = $this->menuModel->find($id);
        
        if (!$menu) {
            return redirect()->to('/admin/menus')->with('error', 'Menu not found.');
        }

        $data = [
            'title' => 'Menu Builder - ' . $menu['name'],
            'menu' => $menu,
            'menuItems' => $this->menuItemModel->getAdminMenuItems($id),
            'availablePages' => $this->menuItemModel->getAvailablePages(),
            'user' => [
                'id' => $this->session->get('user_id'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ]
        ];

        return view('admin/menus/builder', $data);
    }

    /**
     * Duplicate menu
     */
    public function duplicate($id)
    {
        $menu = $this->menuModel->find($id);
        
        if (!$menu) {
            return $this->response->setJSON(['success' => false, 'message' => 'Menu not found.']);
        }

        $newName = $menu['name'] . ' (Copy)';
        
        if ($this->menuModel->duplicateMenu($id, $newName)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Menu duplicated successfully!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to duplicate menu.']);
        }
    }
}
