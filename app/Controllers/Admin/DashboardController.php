<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\PageModel;
use App\Models\BlogPostModel;
use App\Models\MediaModel;

class DashboardController extends BaseController
{
    protected $userModel;
    protected $pageModel;
    protected $blogPostModel;
    protected $mediaModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->pageModel = new PageModel();
        $this->blogPostModel = new BlogPostModel();
        $this->mediaModel = new MediaModel();
        $this->session = session();
    }

    public function index()
    {
        // Get comprehensive statistics
        $stats = $this->getStatistics();

        // Get recent activity
        $recent_activity = $this->getRecentActivity();

        // Get recent posts with more details
        $recent_posts = $this->blogPostModel
            ->select('id, title, slug, excerpt, status, view_count, published_at, created_at')
            ->where('status', 'published')
            ->orderBy('created_at', 'DESC')
            ->limit(5)
            ->findAll();

        $data = [
            'title' => 'Dashboard',
            'user' => [
                'id' => $this->session->get('user_id'),
                'username' => $this->session->get('username'),
                'email' => $this->session->get('email'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ],
            'stats' => $stats,
            'recent_activity' => $recent_activity,
            'recent_posts' => $recent_posts
        ];

        return view('admin/dashboard', $data);
    }

    private function getStatistics()
    {
        // Basic counts
        $total_posts = $this->blogPostModel->countAllResults();
        $total_users = $this->userModel->countAllResults();
        $total_media = $this->mediaModel->countAllResults();

        // Posts this month
        $posts_this_month = $this->blogPostModel
            ->where('created_at >=', date('Y-m-01'))
            ->countAllResults();

        // Total views (sum of all post view counts)
        $total_views_result = $this->blogPostModel
            ->selectSum('view_count', 'total')
            ->get()
            ->getRow();
        $total_views = $total_views_result ? $total_views_result->total : 0;

        // Views this week
        $views_this_week_result = $this->blogPostModel
            ->selectSum('view_count', 'total')
            ->where('updated_at >=', date('Y-m-d', strtotime('-7 days')))
            ->get()
            ->getRow();
        $views_this_week = $views_this_week_result ? $views_this_week_result->total : 0;

        // New users this month
        $new_users_this_month = $this->userModel
            ->where('created_at >=', date('Y-m-01'))
            ->countAllResults();

        // Calculate media size (mock data for now)
        $media_size = $this->calculateMediaSize();

        return [
            'total_posts' => $total_posts,
            'total_views' => $total_views,
            'total_media' => $total_media,
            'total_users' => $total_users,
            'posts_this_month' => $posts_this_month,
            'views_this_week' => $views_this_week,
            'new_users_this_month' => $new_users_this_month,
            'media_size' => $media_size,
            'storage_used' => '75%' // Mock data
        ];
    }

    private function getRecentActivity()
    {
        $activities = [];

        // Recent blog posts
        $recent_posts = $this->blogPostModel
            ->orderBy('created_at', 'DESC')
            ->limit(3)
            ->findAll();

        foreach ($recent_posts as $post) {
            $activities[] = [
                'icon' => 'blog',
                'title' => 'New blog post published',
                'description' => esc($post['title']),
                'time' => $this->timeAgo($post['created_at'])
            ];
        }

        // Recent users (admin only)
        if ($this->session->get('role') === 'admin') {
            $recent_users = $this->userModel
                ->orderBy('created_at', 'DESC')
                ->limit(2)
                ->findAll();

            foreach ($recent_users as $user) {
                $activities[] = [
                    'icon' => 'user-plus',
                    'title' => 'New user registered',
                    'description' => esc($user['first_name'] . ' ' . $user['last_name']),
                    'time' => $this->timeAgo($user['created_at'])
                ];
            }
        }

        // Sort by most recent
        usort($activities, function($a, $b) {
            return strcmp($b['time'], $a['time']);
        });

        return array_slice($activities, 0, 5);
    }

    private function calculateMediaSize()
    {
        // This would calculate actual file sizes in a real implementation
        // For now, return mock data
        return '156 MB';
    }

    private function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . ' minutes ago';
        if ($time < 86400) return floor($time/3600) . ' hours ago';
        if ($time < 2592000) return floor($time/86400) . ' days ago';

        return date('M j, Y', strtotime($datetime));
    }
}
