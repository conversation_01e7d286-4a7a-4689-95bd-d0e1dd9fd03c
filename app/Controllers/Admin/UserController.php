<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use CodeIgniter\HTTP\ResponseInterface;

class UserController extends BaseController
{
    protected $userModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->session = session();
    }

    /**
     * Display users list
     */
    public function index()
    {
        $data = [
            'title' => 'User Management',
            'user' => [
                'id' => $this->session->get('user_id'),
                'username' => $this->session->get('username'),
                'email' => $this->session->get('email'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ],
            'users' => $this->userModel->getAllUsers()
        ];

        return view('admin/users/index', $data);
    }

    /**
     * Show user creation form
     */
    public function create()
    {
        $data = [
            'title' => 'Create User',
            'user' => [
                'id' => $this->session->get('user_id'),
                'username' => $this->session->get('username'),
                'email' => $this->session->get('email'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ]
        ];

        return view('admin/users/create', $data);
    }

    /**
     * Store new user
     */
    public function store()
    {
        $rules = [
            'username' => 'required|min_length[3]|max_length[50]|is_unique[users.username]',
            'email' => 'required|valid_email|is_unique[users.email]',
            'password' => 'required|min_length[6]',
            'first_name' => 'required|max_length[50]',
            'last_name' => 'required|max_length[50]',
            'role' => 'required|in_list[admin,editor,author,subscriber]',
            'status' => 'required|in_list[active,inactive]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $userData = [
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'role' => $this->request->getPost('role'),
            'status' => $this->request->getPost('status'),
            'email_verified_at' => date('Y-m-d H:i:s') // Admin created users are auto-verified
        ];

        if ($this->userModel->insert($userData)) {
            return redirect()->to('/admin/users')->with('success', 'User created successfully!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create user.');
        }
    }

    /**
     * Show user edit form
     */
    public function edit($id)
    {
        $user = $this->userModel->find($id);
        
        if (!$user) {
            return redirect()->to('/admin/users')->with('error', 'User not found.');
        }

        $data = [
            'title' => 'Edit User',
            'user' => [
                'id' => $this->session->get('user_id'),
                'username' => $this->session->get('username'),
                'email' => $this->session->get('email'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ],
            'edit_user' => $user
        ];

        return view('admin/users/edit', $data);
    }

    /**
     * Update user
     */
    public function update($id)
    {
        $user = $this->userModel->find($id);
        
        if (!$user) {
            return redirect()->to('/admin/users')->with('error', 'User not found.');
        }

        $rules = [
            'username' => "required|min_length[3]|max_length[50]|is_unique[users.username,id,{$id}]",
            'email' => "required|valid_email|is_unique[users.email,id,{$id}]",
            'first_name' => 'required|max_length[50]',
            'last_name' => 'required|max_length[50]',
            'role' => 'required|in_list[admin,editor,author,subscriber]',
            'status' => 'required|in_list[active,inactive]'
        ];

        // Only validate password if it's provided
        if ($this->request->getPost('password')) {
            $rules['password'] = 'min_length[6]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $userData = [
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'role' => $this->request->getPost('role'),
            'status' => $this->request->getPost('status')
        ];

        // Only update password if provided
        if ($this->request->getPost('password')) {
            $userData['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
        }

        if ($this->userModel->update($id, $userData)) {
            return redirect()->to('/admin/users')->with('success', 'User updated successfully!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update user.');
        }
    }

    /**
     * Delete user
     */
    public function delete($id)
    {
        // Prevent deletion of current user
        if ($id == $this->session->get('user_id')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot delete your own account.']);
        }

        $user = $this->userModel->find($id);
        
        if (!$user) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not found.']);
        }

        if ($this->userModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'User deleted successfully!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete user.']);
        }
    }

    /**
     * Toggle user status
     */
    public function toggleStatus($id)
    {
        // Prevent status change of current user
        if ($id == $this->session->get('user_id')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Cannot change your own status.']);
        }

        $user = $this->userModel->find($id);
        
        if (!$user) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not found.']);
        }

        $newStatus = $user['status'] === 'active' ? 'inactive' : 'active';

        if ($this->userModel->update($id, ['status' => $newStatus])) {
            return $this->response->setJSON([
                'success' => true, 
                'message' => 'User status updated successfully!',
                'new_status' => $newStatus
            ]);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to update user status.']);
        }
    }

    /**
     * View user profile
     */
    public function profile($id)
    {
        $user = $this->userModel->find($id);
        
        if (!$user) {
            return redirect()->to('/admin/users')->with('error', 'User not found.');
        }

        // Get user statistics
        $stats = $this->userModel->getUserStats($id);

        $data = [
            'title' => 'User Profile',
            'user' => [
                'id' => $this->session->get('user_id'),
                'username' => $this->session->get('username'),
                'email' => $this->session->get('email'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ],
            'profile_user' => $user,
            'stats' => $stats
        ];

        return view('admin/users/profile', $data);
    }

    /**
     * Bulk actions for users
     */
    public function bulkAction()
    {
        $action = $this->request->getPost('action');
        $userIds = $this->request->getPost('user_ids');

        if (!$action || !$userIds) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request.']);
        }

        // Remove current user from bulk actions
        $currentUserId = $this->session->get('user_id');
        $userIds = array_filter($userIds, function($id) use ($currentUserId) {
            return $id != $currentUserId;
        });

        if (empty($userIds)) {
            return $this->response->setJSON(['success' => false, 'message' => 'No valid users selected.']);
        }

        $success = false;
        $message = '';

        switch ($action) {
            case 'activate':
                $success = $this->userModel->whereIn('id', $userIds)->set(['status' => 'active'])->update();
                $message = $success ? 'Users activated successfully!' : 'Failed to activate users.';
                break;
                
            case 'deactivate':
                $success = $this->userModel->whereIn('id', $userIds)->set(['status' => 'inactive'])->update();
                $message = $success ? 'Users deactivated successfully!' : 'Failed to deactivate users.';
                break;
                
            case 'delete':
                $success = $this->userModel->whereIn('id', $userIds)->delete();
                $message = $success ? 'Users deleted successfully!' : 'Failed to delete users.';
                break;
                
            default:
                $message = 'Invalid action.';
        }

        return $this->response->setJSON(['success' => $success, 'message' => $message]);
    }

    /**
     * Verify user email manually (admin action)
     */
    public function verifyEmail($id)
    {
        $user = $this->userModel->find($id);

        if (!$user) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not found.']);
        }

        if (!empty($user['email_verified_at'])) {
            return $this->response->setJSON(['success' => false, 'message' => 'Email is already verified.']);
        }

        if ($this->userModel->update($id, ['email_verified_at' => date('Y-m-d H:i:s')])) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Email verified successfully!'
            ]);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to verify email.']);
        }
    }

    /**
     * Unverify user email (admin action)
     */
    public function unverifyEmail($id)
    {
        $user = $this->userModel->find($id);

        if (!$user) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not found.']);
        }

        if (empty($user['email_verified_at'])) {
            return $this->response->setJSON(['success' => false, 'message' => 'Email is already unverified.']);
        }

        if ($this->userModel->update($id, ['email_verified_at' => null])) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Email verification removed successfully!'
            ]);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Failed to remove email verification.']);
        }
    }
}
