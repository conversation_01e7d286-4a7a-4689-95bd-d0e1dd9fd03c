<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\TemplateModel;
use App\Models\SettingsModel;

class TemplateUpdateController extends BaseController
{
    protected $templateModel;
    protected $settingsModel;
    protected $session;

    public function __construct()
    {
        $this->templateModel = new TemplateModel();
        $this->settingsModel = new SettingsModel();
        $this->session = session();

        // Check if user is logged in and has admin access
        if (!$this->session->get('is_logged_in')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Access denied');
        }

        $userRole = $this->session->get('role');
        if (!in_array($userRole, ['admin', 'editor'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Access denied');
        }
    }

    /**
     * Check for template updates
     */
    public function checkUpdates()
    {
        try {
            $installedTemplates = $this->templateModel->where('template_source', 'templatemo')->findAll();
            $updates = [];

            foreach ($installedTemplates as $template) {
                $updateInfo = $this->checkTemplateUpdate($template);
                if ($updateInfo['hasUpdate']) {
                    $updates[] = $updateInfo;
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'updates' => $updates,
                'count' => count($updates)
            ]);

        } catch (Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to check for updates: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update specific template
     */
    public function updateTemplate($templateId)
    {
        try {
            $template = $this->templateModel->find($templateId);
            
            if (!$template) {
                return $this->response->setJSON(['success' => false, 'message' => 'Template not found.']);
            }

            // Backup current template
            $this->backupTemplate($template);

            // Download and install updated template
            $this->installUpdatedTemplate($template);

            // Update database record
            $this->templateModel->update($templateId, [
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Template updated successfully!'
            ]);

        } catch (Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update template: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Bulk update templates
     */
    public function bulkUpdate()
    {
        try {
            $templateIds = $this->request->getPost('template_ids');
            
            if (empty($templateIds)) {
                return $this->response->setJSON(['success' => false, 'message' => 'No templates selected.']);
            }

            $results = [];
            foreach ($templateIds as $templateId) {
                $template = $this->templateModel->find($templateId);
                if ($template) {
                    try {
                        $this->backupTemplate($template);
                        $this->installUpdatedTemplate($template);
                        $this->templateModel->update($templateId, ['updated_at' => date('Y-m-d H:i:s')]);
                        $results[] = ['id' => $templateId, 'success' => true, 'name' => $template['name']];
                    } catch (Exception $e) {
                        $results[] = ['id' => $templateId, 'success' => false, 'name' => $template['name'], 'error' => $e->getMessage()];
                    }
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'results' => $results,
                'message' => 'Bulk update completed!'
            ]);

        } catch (Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Bulk update failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get template update history
     */
    public function getUpdateHistory($templateId)
    {
        $template = $this->templateModel->find($templateId);
        
        if (!$template) {
            return $this->response->setJSON(['success' => false, 'message' => 'Template not found.']);
        }

        // Get update history from settings or log files
        $history = $this->getTemplateHistory($template);

        return $this->response->setJSON([
            'success' => true,
            'history' => $history
        ]);
    }

    /**
     * Rollback template to previous version
     */
    public function rollback($templateId)
    {
        try {
            $template = $this->templateModel->find($templateId);
            
            if (!$template) {
                return $this->response->setJSON(['success' => false, 'message' => 'Template not found.']);
            }

            $backupPath = $this->getBackupPath($template);
            if (!is_dir($backupPath)) {
                return $this->response->setJSON(['success' => false, 'message' => 'No backup found for this template.']);
            }

            // Restore from backup
            $this->restoreFromBackup($template);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Template rolled back successfully!'
            ]);

        } catch (Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Rollback failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Check if template has updates available
     */
    private function checkTemplateUpdate($template)
    {
        // This would typically check against TemplateMonster API
        // For demo purposes, we'll simulate update checking
        
        $currentVersion = $template['updated_at'] ?? $template['created_at'];
        $lastCheck = strtotime($currentVersion);
        $daysSinceUpdate = (time() - $lastCheck) / (24 * 60 * 60);
        
        // Simulate updates available for templates older than 30 days
        $hasUpdate = $daysSinceUpdate > 30;
        
        return [
            'template_id' => $template['id'],
            'template_name' => $template['name'],
            'current_version' => date('Y-m-d', $lastCheck),
            'latest_version' => date('Y-m-d'),
            'hasUpdate' => $hasUpdate,
            'updateSize' => $hasUpdate ? rand(500, 2000) . ' KB' : null,
            'changelog' => $hasUpdate ? $this->getSimulatedChangelog($template) : null
        ];
    }

    /**
     * Backup template before update
     */
    private function backupTemplate($template)
    {
        $backupDir = $this->getBackupPath($template);
        
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }

        // Backup template files
        $templateDir = APPPATH . 'Views/templates/templatemo/' . $template['template_id'];
        if (is_dir($templateDir)) {
            $this->copyDirectory($templateDir, $backupDir . '/views');
        }

        // Backup assets
        $assetsDir = FCPATH . 'assets/templates/' . $template['template_id'];
        if (is_dir($assetsDir)) {
            $this->copyDirectory($assetsDir, $backupDir . '/assets');
        }

        // Save backup info
        file_put_contents($backupDir . '/backup_info.json', json_encode([
            'template_id' => $template['id'],
            'template_name' => $template['name'],
            'backup_date' => date('Y-m-d H:i:s'),
            'version' => $template['updated_at'] ?? $template['created_at']
        ], JSON_PRETTY_PRINT));
    }

    /**
     * Install updated template
     */
    private function installUpdatedTemplate($template)
    {
        // This would download the latest version from TemplateMonster
        // For demo purposes, we'll simulate the update process
        
        $templateDir = APPPATH . 'Views/templates/templatemo/' . $template['template_id'];
        $assetsDir = FCPATH . 'assets/templates/' . $template['template_id'];

        // Create updated template content (simulation)
        $updatedContent = $this->generateUpdatedTemplate($template);
        
        if (!is_dir($templateDir)) {
            mkdir($templateDir, 0755, true);
        }
        
        file_put_contents($templateDir . '/index.php', $updatedContent);

        // Update assets (simulation)
        if (!is_dir($assetsDir . '/css')) {
            mkdir($assetsDir . '/css', 0755, true);
        }
        
        $updatedCSS = "/* Updated CSS for {$template['name']} - " . date('Y-m-d') . " */\n";
        $updatedCSS .= "/* New features and improvements */\n";
        file_put_contents($assetsDir . '/css/templatemo-style.css', $updatedCSS);
    }

    /**
     * Get backup path for template
     */
    private function getBackupPath($template)
    {
        return WRITEPATH . 'backups/templates/' . $template['template_id'] . '/' . date('Y-m-d_H-i-s');
    }

    /**
     * Copy directory recursively
     */
    private function copyDirectory($source, $destination)
    {
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($source, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $item) {
            $target = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            if ($item->isDir()) {
                mkdir($target, 0755, true);
            } else {
                copy($item, $target);
            }
        }
    }

    /**
     * Generate updated template content
     */
    private function generateUpdatedTemplate($template)
    {
        return '<?= $this->extend(\'layouts/templatemo\') ?>

<?= $this->section(\'title\') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section(\'meta\') ?>
<meta name="description" content="<?= esc($meta_description ?? \'\') ?>">
<meta name="keywords" content="<?= esc($meta_keywords ?? \'\') ?>">
<?= $this->endSection() ?>

<?= $this->section(\'styles\') ?>
<link rel="stylesheet" href="<?= base_url(\'assets/templates/' . $template['template_id'] . '/css/bootstrap.min.css\') ?>">
<link rel="stylesheet" href="<?= base_url(\'assets/templates/' . $template['template_id'] . '/css/templatemo-style.css\') ?>">
<!-- Updated: ' . date('Y-m-d H:i:s') . ' -->
<?= $this->endSection() ?>

<?= $this->section(\'content\') ?>
<!-- ' . $template['name'] . ' Template - Updated Version -->
<div class="template-content updated-template">
    <div class="update-notice bg-info text-white p-2 mb-3 rounded">
        <small><i class="fas fa-info-circle me-1"></i>Template updated on ' . date('Y-m-d') . '</small>
    </div>
    <?= $content ?>
</div>
<?= $this->endSection() ?>

<?= $this->section(\'scripts\') ?>
<script src="<?= base_url(\'assets/templates/' . $template['template_id'] . '/js/jquery.min.js\') ?>"></script>
<script src="<?= base_url(\'assets/templates/' . $template['template_id'] . '/js/bootstrap.min.js\') ?>"></script>
<script src="<?= base_url(\'assets/templates/' . $template['template_id'] . '/js/templatemo-script.js\') ?>"></script>
<?= $this->endSection() ?>';
    }

    /**
     * Get simulated changelog
     */
    private function getSimulatedChangelog($template)
    {
        $changelogs = [
            'Bug fixes and performance improvements',
            'Updated Bootstrap to latest version',
            'Added new color scheme options',
            'Improved mobile responsiveness',
            'Enhanced accessibility features',
            'Updated font icons and graphics',
            'Security improvements',
            'New animation effects'
        ];

        return array_slice($changelogs, 0, rand(2, 4));
    }

    /**
     * Get template history
     */
    private function getTemplateHistory($template)
    {
        // This would typically come from a database or log files
        return [
            [
                'date' => date('Y-m-d H:i:s'),
                'action' => 'Current Version',
                'version' => '1.2.0',
                'user' => $this->session->get('username')
            ],
            [
                'date' => date('Y-m-d H:i:s', strtotime('-7 days')),
                'action' => 'Updated',
                'version' => '1.1.0',
                'user' => $this->session->get('username')
            ],
            [
                'date' => $template['created_at'],
                'action' => 'Installed',
                'version' => '1.0.0',
                'user' => $this->session->get('username')
            ]
        ];
    }

    /**
     * Restore template from backup
     */
    private function restoreFromBackup($template)
    {
        $backupDir = $this->getBackupPath($template);
        
        // Restore views
        if (is_dir($backupDir . '/views')) {
            $templateDir = APPPATH . 'Views/templates/templatemo/' . $template['template_id'];
            $this->copyDirectory($backupDir . '/views', $templateDir);
        }

        // Restore assets
        if (is_dir($backupDir . '/assets')) {
            $assetsDir = FCPATH . 'assets/templates/' . $template['template_id'];
            $this->copyDirectory($backupDir . '/assets', $assetsDir);
        }
    }
}
