# 🚀 Development Guide

## 🎯 Overview

This guide provides step-by-step instructions for developers working with the admin interface. It covers how to create new pages, extend existing functionality, and maintain the codebase.

## 🏗️ Creating New Admin Pages

### Step 1: Create the View File

Create a new PHP file in the appropriate admin subdirectory:

```php
<?php
// app/Views/admin/your-section/your-page.php
?>
<?= $this->extend('layouts/admin') ?>

<?= $this->section('title') ?>Your Page Title<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<a href="<?= base_url('admin') ?>">Admin</a>
<i class="fas fa-chevron-right"></i>
<a href="<?= base_url('admin/your-section') ?>">Your Section</a>
<i class="fas fa-chevron-right"></i>
<span>Your Page</span>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<!-- Optional: Page-specific CSS -->
<style>
    .custom-component {
        /* Your custom styles */
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Your Page Title</h1>
        <p class="page-subtitle">Brief description of what this page does</p>
    </div>
    <div class="page-actions">
        <a href="#" class="btn btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New
        </a>
    </div>
</div>

<!-- Page Content -->
<div class="row">
    <div class="col-12">
        <div class="admin-card">
            <div class="admin-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Content Title
                </h5>
            </div>
            <div class="admin-card-body">
                <!-- Your content here -->
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Page-specific JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize page functionality
    });
</script>
<?= $this->endSection() ?>
```

### Step 2: Create the Controller

```php
<?php
// app/Controllers/Admin/YourSection.php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;

class YourSection extends BaseController
{
    public function index()
    {
        $data = [
            'title' => 'Your Section',
            'user' => session()->get('user'), // Current user data
            'items' => $this->getItems() // Your data
        ];
        
        return view('admin/your-section/index', $data);
    }
    
    public function create()
    {
        $data = [
            'title' => 'Create New Item',
            'user' => session()->get('user')
        ];
        
        return view('admin/your-section/create', $data);
    }
    
    public function store()
    {
        // Validation
        $validation = \Config\Services::validation();
        $validation->setRules([
            'name' => 'required|min_length[3]',
            'email' => 'required|valid_email'
        ]);
        
        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()
                ->withInput()
                ->with('errors', $validation->getErrors());
        }
        
        // Save logic here
        
        return redirect()->to('admin/your-section')
            ->with('success', 'Item created successfully!');
    }
    
    private function getItems()
    {
        // Your data retrieval logic
        return [];
    }
}
```

### Step 3: Add Routes

```php
// app/Config/Routes.php

$routes->group('admin', ['filter' => 'auth'], function($routes) {
    // Your section routes
    $routes->get('your-section', 'Admin\YourSection::index');
    $routes->get('your-section/create', 'Admin\YourSection::create');
    $routes->post('your-section/store', 'Admin\YourSection::store');
    $routes->get('your-section/edit/(:num)', 'Admin\YourSection::edit/$1');
    $routes->post('your-section/update/(:num)', 'Admin\YourSection::update/$1');
    $routes->delete('your-section/delete/(:num)', 'Admin\YourSection::delete/$1');
});
```

### Step 4: Update Navigation

Add your new section to the admin sidebar in `app/Views/layouts/admin.php`:

```php
<a class="admin-nav-link" href="<?= base_url('admin/your-section') ?>">
    <i class="fas fa-your-icon"></i>
    <span>Your Section</span>
</a>
```

## 📝 Form Development Pattern

### Basic Form Structure

```html
<form action="<?= base_url('admin/your-section/store') ?>" method="post" id="yourForm">
    <div class="row">
        <div class="col-lg-8">
            <!-- Main Content -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Basic Information
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="name" class="admin-form-label">Name *</label>
                        <input type="text" class="admin-form-control" id="name" name="name" 
                               value="<?= old('name') ?>" required>
                        <div class="admin-form-text">Enter a descriptive name</div>
                    </div>
                    
                    <div class="admin-form-group mb-0">
                        <label for="description" class="admin-form-label">Description</label>
                        <textarea class="admin-form-control" id="description" name="description" 
                                  rows="4"><?= old('description') ?></textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Sidebar -->
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Settings
                    </h5>
                </div>
                <div class="admin-card-body">
                    <div class="admin-form-group">
                        <label for="status" class="admin-form-label">Status</label>
                        <select class="admin-form-control" id="status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-admin-primary">
                            <i class="fas fa-save me-2"></i>Save
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
```

### Form JavaScript Pattern

```javascript
document.getElementById('yourForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Validate form
    if (!validateForm('yourForm')) {
        showAlert('Please fix form errors', 'error');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    toggleLoading(submitBtn, true);
    
    // Submit form
    const formData = new FormData(this);
    
    adminAPI(this.action, {
        method: 'POST',
        body: formData
    })
    .then(data => {
        if (data.success) {
            showAlert('Saved successfully!', 'success');
            // Optionally redirect
            setTimeout(() => {
                window.location.href = data.redirect || '/admin/your-section';
            }, 1000);
        } else {
            showAlert(data.message || 'Failed to save', 'error');
        }
    })
    .catch(error => {
        showAlert('An error occurred', 'error');
    })
    .finally(() => {
        toggleLoading(submitBtn, false);
    });
});
```

## 📊 Data Table Development

### Table HTML Structure

```html
<div class="admin-table-responsive">
    <table class="admin-table" id="dataTable">
        <thead>
            <tr>
                <th>
                    <input type="checkbox" class="admin-form-check-input" id="selectAll">
                </th>
                <th data-sortable="name">Name</th>
                <th data-sortable="status">Status</th>
                <th data-sortable="created_at">Created</th>
                <th class="admin-table-actions">Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($items as $item): ?>
            <tr>
                <td>
                    <input type="checkbox" class="admin-form-check-input row-checkbox" 
                           value="<?= $item['id'] ?>">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <strong><?= esc($item['name']) ?></strong>
                    </div>
                </td>
                <td>
                    <?= status_badge($item['status']) ?>
                </td>
                <td>
                    <span class="text-muted"><?= date('M j, Y', strtotime($item['created_at'])) ?></span>
                </td>
                <td class="admin-table-actions">
                    <div class="btn-group">
                        <a href="<?= base_url('admin/your-section/edit/' . $item['id']) ?>" 
                           class="btn btn-admin-secondary btn-sm">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button type="button" class="btn btn-admin-danger btn-sm" 
                                onclick="deleteItem(<?= $item['id'] ?>)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<!-- Bulk Actions Bar -->
<div id="bulk-actions" class="admin-bulk-actions" style="display: none;">
    <div class="d-flex align-items-center justify-content-between">
        <span><span class="selected-count">0</span> items selected</span>
        <div class="btn-group">
            <button type="button" class="btn btn-admin-warning btn-sm" onclick="bulkAction('activate')">
                <i class="fas fa-check me-1"></i>Activate
            </button>
            <button type="button" class="btn btn-admin-secondary btn-sm" onclick="bulkAction('deactivate')">
                <i class="fas fa-times me-1"></i>Deactivate
            </button>
            <button type="button" class="btn btn-admin-danger btn-sm" onclick="bulkAction('delete')">
                <i class="fas fa-trash me-1"></i>Delete
            </button>
        </div>
    </div>
</div>
```

### Table JavaScript

```javascript
// Initialize table functionality
document.addEventListener('DOMContentLoaded', function() {
    initDataTable('dataTable');
    initBulkActions('dataTable');
});

// Delete single item
function deleteItem(id) {
    confirmDelete('Are you sure you want to delete this item?', function() {
        adminAPI(`/admin/your-section/delete/${id}`, {
            method: 'DELETE'
        })
        .then(data => {
            if (data.success) {
                showAlert('Item deleted successfully', 'success');
                location.reload();
            } else {
                showAlert(data.message || 'Failed to delete item', 'error');
            }
        })
        .catch(error => {
            showAlert('An error occurred', 'error');
        });
    });
}

// Bulk actions
function bulkAction(action) {
    const selectedIds = Array.from(document.querySelectorAll('.row-checkbox:checked'))
        .map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        showAlert('Please select items first', 'warning');
        return;
    }
    
    const actionText = action.charAt(0).toUpperCase() + action.slice(1);
    
    confirmAction(`Are you sure you want to ${action} ${selectedIds.length} items?`, function() {
        adminAPI('/admin/your-section/bulk-action', {
            method: 'POST',
            body: JSON.stringify({
                action: action,
                ids: selectedIds
            })
        })
        .then(data => {
            if (data.success) {
                showAlert(`${actionText} completed successfully`, 'success');
                location.reload();
            } else {
                showAlert(data.message || `Failed to ${action} items`, 'error');
            }
        })
        .catch(error => {
            showAlert('An error occurred', 'error');
        });
    });
}
```

## 🎨 Custom Component Development

### Creating a Custom Component

```css
/* Add to your page's head section */
.custom-widget {
    background: white;
    border-radius: var(--admin-radius-lg);
    box-shadow: var(--admin-shadow);
    border: 1px solid var(--admin-border);
    padding: 1.5rem;
    transition: transform 0.2s ease;
}

.custom-widget:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
}

.custom-widget-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.custom-widget-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--admin-radius);
    background: var(--admin-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.custom-widget-content h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--admin-text);
}

.custom-widget-content p {
    margin: 0;
    color: var(--admin-text-muted);
    font-size: 0.875rem;
}
```

```html
<!-- HTML Structure -->
<div class="custom-widget">
    <div class="custom-widget-header">
        <div class="custom-widget-icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="custom-widget-content">
            <h3>1,234</h3>
            <p>Total Items</p>
        </div>
    </div>
</div>
```

## 🔧 Best Practices

### Code Organization
1. **Follow MVC Pattern**: Keep logic in controllers, data in models, presentation in views
2. **Use Consistent Naming**: Follow CodeIgniter naming conventions
3. **Validate Input**: Always validate and sanitize user input
4. **Handle Errors**: Provide meaningful error messages to users
5. **Use Transactions**: For database operations that affect multiple tables

### Security Considerations
1. **CSRF Protection**: Use CodeIgniter's CSRF protection
2. **Input Validation**: Validate all user input server-side
3. **SQL Injection**: Use query builder or prepared statements
4. **XSS Prevention**: Escape output using `esc()` function
5. **Authentication**: Check user permissions for admin actions

### Performance Optimization
1. **Database Queries**: Optimize queries and use indexes
2. **Caching**: Implement caching for frequently accessed data
3. **Asset Optimization**: Minify CSS and JavaScript in production
4. **Image Optimization**: Compress and resize images appropriately
5. **Pagination**: Implement pagination for large datasets

### Testing
1. **Unit Tests**: Test individual functions and methods
2. **Integration Tests**: Test complete workflows
3. **Browser Testing**: Test across different browsers and devices
4. **Performance Testing**: Test with realistic data volumes
5. **Security Testing**: Test for common vulnerabilities

## 📚 Common Patterns

### AJAX Form Submission
```javascript
function submitFormAjax(formId, successCallback) {
    const form = document.getElementById(formId);
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!validateForm(formId)) {
            showAlert('Please fix form errors', 'error');
            return;
        }
        
        const submitBtn = form.querySelector('button[type="submit"]');
        toggleLoading(submitBtn, true);
        
        adminAPI(form.action, {
            method: form.method.toUpperCase(),
            body: new FormData(form)
        })
        .then(data => {
            if (data.success) {
                showAlert(data.message || 'Success!', 'success');
                if (successCallback) successCallback(data);
            } else {
                showAlert(data.message || 'Error occurred', 'error');
            }
        })
        .catch(error => {
            showAlert('Network error occurred', 'error');
        })
        .finally(() => {
            toggleLoading(submitBtn, false);
        });
    });
}
```

### Modal Integration
```javascript
function openModal(modalId, data = {}) {
    const modal = document.getElementById(modalId);
    
    // Populate modal with data
    Object.keys(data).forEach(key => {
        const element = modal.querySelector(`[name="${key}"]`);
        if (element) element.value = data[key];
    });
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}
```

This development guide provides the foundation for creating consistent, maintainable admin pages that follow the established patterns and provide excellent user experience.
