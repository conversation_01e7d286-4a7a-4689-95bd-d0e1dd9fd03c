# ⚡ JavaScript Utilities Documentation

## 🎯 Overview

This document details all the JavaScript utilities and functions available in the admin interface. These utilities provide consistent functionality across all admin pages and enhance the user experience with modern interactions.

## 🔧 Core Utility Functions

### API Helper Function

#### `adminAPI(url, options = {})`
Centralized AJAX handling with consistent error management and response processing.

```javascript
function adminAPI(url, options = {}) {
    const defaults = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };
    
    const config = { ...defaults, ...options };
    
    return fetch(url, config)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('API Error:', error);
            throw error;
        });
}
```

**Usage Examples:**
```javascript
// GET request
adminAPI('/admin/api/users')
    .then(data => console.log(data))
    .catch(error => showAlert('Failed to load users', 'error'));

// POST request
adminAPI('/admin/api/users', {
    method: 'POST',
    body: JSON.stringify({ name: 'John', email: '<EMAIL>' })
})
.then(data => showAlert('User created successfully', 'success'))
.catch(error => showAlert('Failed to create user', 'error'));

// DELETE request
adminAPI('/admin/api/users/123', { method: 'DELETE' })
    .then(data => showAlert('User deleted', 'success'))
    .catch(error => showAlert('Failed to delete user', 'error'));
```

## 🔔 User Feedback Functions

### Alert System

#### `showAlert(message, type = 'info', duration = 5000)`
Displays toast-style notifications to users.

```javascript
function showAlert(message, type = 'info', duration = 5000) {
    const alertTypes = {
        'success': { icon: 'fas fa-check-circle', class: 'alert-success' },
        'error': { icon: 'fas fa-exclamation-triangle', class: 'alert-danger' },
        'warning': { icon: 'fas fa-exclamation-circle', class: 'alert-warning' },
        'info': { icon: 'fas fa-info-circle', class: 'alert-info' }
    };
    
    const config = alertTypes[type] || alertTypes['info'];
    
    const alertHtml = `
        <div class="alert ${config.class} alert-dismissible fade show admin-toast" role="alert">
            <i class="${config.icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.getElementById('alert-container') || createAlertContainer();
    container.insertAdjacentHTML('beforeend', alertHtml);
    
    // Auto-dismiss after duration
    setTimeout(() => {
        const alert = container.lastElementChild;
        if (alert) {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }
    }, duration);
}
```

**Usage Examples:**
```javascript
showAlert('Data saved successfully!', 'success');
showAlert('Please fill in all required fields', 'error');
showAlert('This action cannot be undone', 'warning');
showAlert('Loading data...', 'info', 2000);
```

### Confirmation Dialogs

#### `confirmAction(message, callback, options = {})`
Professional confirmation dialog for user actions.

```javascript
function confirmAction(message, callback, options = {}) {
    const defaults = {
        title: 'Confirm Action',
        confirmText: 'Confirm',
        cancelText: 'Cancel',
        type: 'primary'
    };
    
    const config = { ...defaults, ...options };
    
    const modalHtml = `
        <div class="modal fade" id="confirmModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${config.title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-admin-secondary" data-bs-dismiss="modal">
                            ${config.cancelText}
                        </button>
                        <button type="button" class="btn btn-admin-${config.type}" id="confirmBtn">
                            ${config.confirmText}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal
    const existingModal = document.getElementById('confirmModal');
    if (existingModal) existingModal.remove();
    
    // Add new modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    
    document.getElementById('confirmBtn').addEventListener('click', () => {
        modal.hide();
        callback();
    });
    
    modal.show();
}
```

#### `confirmDelete(message, callback)`
Specialized confirmation for delete actions.

```javascript
function confirmDelete(message, callback) {
    confirmAction(message, callback, {
        title: 'Confirm Deletion',
        confirmText: 'Delete',
        type: 'danger'
    });
}
```

**Usage Examples:**
```javascript
confirmAction('Are you sure you want to save changes?', () => {
    // Save logic here
});

confirmDelete('This will permanently delete the item. Continue?', () => {
    // Delete logic here
});
```

## 📝 Form Utilities

### Form Validation

#### `validateForm(formId)`
Client-side form validation with visual feedback.

```javascript
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        const value = field.value.trim();
        
        if (!value) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        }
        
        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                field.classList.add('is-invalid');
                isValid = false;
            }
        }
        
        // URL validation
        if (field.type === 'url' && value) {
            try {
                new URL(value);
                field.classList.remove('is-invalid');
            } catch {
                field.classList.add('is-invalid');
                isValid = false;
            }
        }
    });
    
    return isValid;
}
```

### Loading States

#### `toggleLoading(button, loading = true)`
Manages button loading states to prevent double submissions.

```javascript
function toggleLoading(button, loading = true) {
    if (!button) return;
    
    if (loading) {
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
        button.disabled = true;
    } else {
        button.innerHTML = button.dataset.originalText || button.innerHTML;
        button.disabled = false;
        delete button.dataset.originalText;
    }
}
```

**Usage Examples:**
```javascript
// Form submission with validation and loading
document.getElementById('myForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (!validateForm('myForm')) {
        showAlert('Please fix form errors', 'error');
        return;
    }
    
    const submitBtn = this.querySelector('button[type="submit"]');
    toggleLoading(submitBtn, true);
    
    // Simulate API call
    adminAPI('/admin/api/save', {
        method: 'POST',
        body: new FormData(this)
    })
    .then(data => {
        showAlert('Saved successfully!', 'success');
        // Optionally redirect or reset form
    })
    .catch(error => {
        showAlert('Failed to save', 'error');
    })
    .finally(() => {
        toggleLoading(submitBtn, false);
    });
});
```

## 🔍 Data Management Utilities

### Table Utilities

#### `initDataTable(tableId, options = {})`
Initialize enhanced data tables with sorting, filtering, and pagination.

```javascript
function initDataTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const defaults = {
        sortable: true,
        filterable: true,
        paginated: true,
        pageSize: 10
    };
    
    const config = { ...defaults, ...options };
    
    // Add sorting functionality
    if (config.sortable) {
        const headers = table.querySelectorAll('th[data-sortable]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => sortTable(table, header));
        });
    }
    
    // Add filtering
    if (config.filterable) {
        addTableFilter(table);
    }
    
    // Add pagination
    if (config.paginated) {
        addTablePagination(table, config.pageSize);
    }
}
```

### Bulk Actions

#### `initBulkActions(tableId)`
Initialize bulk action functionality for data tables.

```javascript
function initBulkActions(tableId) {
    const table = document.getElementById(tableId);
    const selectAllCheckbox = table.querySelector('thead input[type="checkbox"]');
    const rowCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]');
    const bulkActionBar = document.getElementById('bulk-actions');
    
    // Select all functionality
    selectAllCheckbox?.addEventListener('change', function() {
        rowCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionBar();
    });
    
    // Individual checkbox handling
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionBar);
    });
    
    function updateBulkActionBar() {
        const selectedCount = Array.from(rowCheckboxes).filter(cb => cb.checked).length;
        
        if (selectedCount > 0) {
            bulkActionBar.style.display = 'block';
            bulkActionBar.querySelector('.selected-count').textContent = selectedCount;
        } else {
            bulkActionBar.style.display = 'none';
        }
        
        // Update select all checkbox state
        if (selectAllCheckbox) {
            selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < rowCheckboxes.length;
            selectAllCheckbox.checked = selectedCount === rowCheckboxes.length;
        }
    }
}
```

## 🎨 UI Enhancement Utilities

### Auto-Generation Functions

#### `generateSlug(text)`
Generate URL-friendly slugs from text input.

```javascript
function generateSlug(text) {
    return text
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9 -]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-+|-+$/g, '');
}

// Auto-slug generation for forms
function initAutoSlug(titleFieldId, slugFieldId) {
    const titleField = document.getElementById(titleFieldId);
    const slugField = document.getElementById(slugFieldId);
    
    if (!titleField || !slugField) return;
    
    titleField.addEventListener('input', function() {
        if (!slugField.dataset.manualEdit) {
            const slug = generateSlug(this.value);
            slugField.value = slug;
            
            // Update any preview elements
            const preview = document.getElementById('slug-preview');
            if (preview) preview.textContent = slug || 'page-slug';
        }
    });
    
    slugField.addEventListener('input', function() {
        this.dataset.manualEdit = 'true';
        const slug = generateSlug(this.value);
        this.value = slug;
        
        // Update preview
        const preview = document.getElementById('slug-preview');
        if (preview) preview.textContent = slug || 'page-slug';
    });
}
```

### Character Counters

#### `initCharacterCounter(fieldId, counterId, maxLength)`
Add character counting functionality to text fields.

```javascript
function initCharacterCounter(fieldId, counterId, maxLength) {
    const field = document.getElementById(fieldId);
    const counter = document.getElementById(counterId);
    
    if (!field || !counter) return;
    
    function updateCounter() {
        const length = field.value.length;
        counter.textContent = length;
        
        // Color coding based on length
        counter.className = '';
        if (length > maxLength) {
            counter.classList.add('text-danger');
        } else if (length > maxLength * 0.8) {
            counter.classList.add('text-warning');
        } else {
            counter.classList.add('text-success');
        }
    }
    
    field.addEventListener('input', updateCounter);
    updateCounter(); // Initial count
}
```

## 🚀 Initialization Functions

### Page Initialization
```javascript
// Initialize common functionality when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all data tables
    document.querySelectorAll('.admin-table').forEach(table => {
        if (table.id) {
            initDataTable(table.id);
            initBulkActions(table.id);
        }
    });
    
    // Initialize auto-slug generation
    if (document.getElementById('title') && document.getElementById('slug')) {
        initAutoSlug('title', 'slug');
    }
    
    // Initialize character counters
    const counters = document.querySelectorAll('[data-character-counter]');
    counters.forEach(field => {
        const counterId = field.dataset.characterCounter;
        const maxLength = field.getAttribute('maxlength');
        if (counterId && maxLength) {
            initCharacterCounter(field.id, counterId, parseInt(maxLength));
        }
    });
    
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => new bootstrap.Tooltip(tooltip));
});
```

These JavaScript utilities provide a comprehensive foundation for building interactive, user-friendly admin interfaces. They handle common patterns like form validation, AJAX requests, user feedback, and data management while maintaining consistency across all admin pages.
