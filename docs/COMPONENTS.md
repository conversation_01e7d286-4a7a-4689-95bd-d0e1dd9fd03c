# 🎨 Admin Components Documentation

## 📋 Component Library Overview

This document details all the reusable components available in the admin interface. Each component follows consistent design patterns and can be easily implemented across different pages.

## 🏗️ Layout Components

### Admin Card
The primary container component for content sections.

```html
<div class="admin-card">
    <div class="admin-card-header">
        <h5 class="mb-0">
            <i class="fas fa-icon me-2"></i>Card Title
        </h5>
        <div class="admin-card-actions">
            <button class="btn btn-admin-secondary btn-sm">Action</button>
        </div>
    </div>
    <div class="admin-card-body">
        <!-- Main content goes here -->
    </div>
    <div class="admin-card-footer">
        <!-- Footer actions or info -->
    </div>
</div>
```

**Features:**
- Consistent shadow and border radius
- Optional header with title and actions
- Flexible body content area
- Optional footer for actions

### Page Header
Standardized page header with title, subtitle, and actions.

```html
<div class="page-header d-flex justify-content-between align-items-start">
    <div>
        <h1 class="page-title">Page Title</h1>
        <p class="page-subtitle">Brief description of the page</p>
    </div>
    <div class="page-actions">
        <a href="#" class="btn btn-admin-primary">
            <i class="fas fa-plus me-2"></i>Add New
        </a>
    </div>
</div>
```

**Features:**
- Responsive layout (stacks on mobile)
- Icon support in titles
- Multiple action buttons supported
- Consistent spacing and typography

## 📝 Form Components

### Form Group
Container for form fields with label, input, and help text.

```html
<div class="admin-form-group">
    <label for="field-id" class="admin-form-label">
        Field Label <span class="text-danger">*</span>
    </label>
    <input type="text" id="field-id" name="field-name" 
           class="admin-form-control" required>
    <div class="admin-form-text">
        Help text or validation message
    </div>
</div>
```

**Variations:**
```html
<!-- Select Dropdown -->
<select class="admin-form-control">
    <option value="">Choose option</option>
    <option value="1">Option 1</option>
</select>

<!-- Textarea -->
<textarea class="admin-form-control" rows="4"></textarea>

<!-- File Input -->
<input type="file" class="admin-form-control">

<!-- Checkbox -->
<div class="admin-form-check">
    <input type="checkbox" id="check" class="admin-form-check-input">
    <label for="check" class="admin-form-check-label">Check me</label>
</div>
```

### Form Validation States
```html
<!-- Success State -->
<input class="admin-form-control is-valid">
<div class="admin-form-feedback valid-feedback">
    Looks good!
</div>

<!-- Error State -->
<input class="admin-form-control is-invalid">
<div class="admin-form-feedback invalid-feedback">
    Please provide a valid input.
</div>
```

## 📊 Table Components

### Admin Table
Responsive data table with consistent styling.

```html
<div class="admin-table-responsive">
    <table class="admin-table">
        <thead>
            <tr>
                <th>
                    <input type="checkbox" class="admin-form-check-input">
                </th>
                <th>Name</th>
                <th>Status</th>
                <th>Date</th>
                <th class="admin-table-actions">Actions</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <input type="checkbox" class="admin-form-check-input">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <img src="avatar.jpg" class="admin-avatar me-2">
                        <span>John Doe</span>
                    </div>
                </td>
                <td>
                    <span class="badge bg-admin-success">Active</span>
                </td>
                <td>
                    <span class="text-muted">2024-01-15</span>
                </td>
                <td class="admin-table-actions">
                    <div class="btn-group">
                        <button class="btn btn-admin-secondary btn-sm">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-admin-danger btn-sm">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>
```

**Features:**
- Responsive horizontal scrolling
- Sortable columns (with JavaScript)
- Bulk selection checkboxes
- Action button groups
- Status badges

## 🔘 Button Components

### Button Variants
```html
<!-- Primary Actions -->
<button class="btn btn-admin-primary">
    <i class="fas fa-save me-2"></i>Save
</button>

<!-- Secondary Actions -->
<button class="btn btn-admin-secondary">
    <i class="fas fa-edit me-2"></i>Edit
</button>

<!-- Success Actions -->
<button class="btn btn-admin-success">
    <i class="fas fa-check me-2"></i>Approve
</button>

<!-- Warning Actions -->
<button class="btn btn-admin-warning">
    <i class="fas fa-exclamation me-2"></i>Warning
</button>

<!-- Danger Actions -->
<button class="btn btn-admin-danger">
    <i class="fas fa-trash me-2"></i>Delete
</button>

<!-- Info Actions -->
<button class="btn btn-admin-info">
    <i class="fas fa-info me-2"></i>Info
</button>
```

### Button Sizes
```html
<button class="btn btn-admin-primary btn-sm">Small</button>
<button class="btn btn-admin-primary">Default</button>
<button class="btn btn-admin-primary btn-lg">Large</button>
```

### Button Groups
```html
<div class="btn-group" role="group">
    <button class="btn btn-admin-secondary">Left</button>
    <button class="btn btn-admin-secondary">Middle</button>
    <button class="btn btn-admin-secondary">Right</button>
</div>
```

## 🏷️ Badge Components

### Status Badges
```html
<span class="badge bg-admin-success">Active</span>
<span class="badge bg-admin-warning">Pending</span>
<span class="badge bg-admin-danger">Inactive</span>
<span class="badge bg-admin-info">Draft</span>
<span class="badge bg-admin-secondary">Archived</span>
```

### Helper Function
```php
<?php
function status_badge($status) {
    $classes = [
        'active' => 'bg-admin-success',
        'published' => 'bg-admin-success',
        'pending' => 'bg-admin-warning',
        'draft' => 'bg-admin-info',
        'inactive' => 'bg-admin-danger',
        'archived' => 'bg-admin-secondary'
    ];
    
    $class = $classes[$status] ?? 'bg-admin-secondary';
    return "<span class=\"badge {$class}\">" . ucfirst($status) . "</span>";
}
?>
```

## 🚫 Empty State Components

### Empty State Design
```html
<div class="admin-empty-state">
    <i class="fas fa-inbox"></i>
    <h4>No Items Found</h4>
    <p>Get started by creating your first item.</p>
    <a href="#" class="btn btn-admin-primary">
        <i class="fas fa-plus me-2"></i>Create Item
    </a>
</div>
```

**Features:**
- Centered layout
- Large icon
- Descriptive text
- Call-to-action button

## 🔔 Alert Components

### Alert Types
```html
<!-- Success Alert -->
<div class="alert alert-admin-success alert-dismissible fade show">
    <i class="fas fa-check-circle me-2"></i>
    Success message here
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>

<!-- Error Alert -->
<div class="alert alert-admin-danger alert-dismissible fade show">
    <i class="fas fa-exclamation-triangle me-2"></i>
    Error message here
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>

<!-- Warning Alert -->
<div class="alert alert-admin-warning alert-dismissible fade show">
    <i class="fas fa-exclamation-circle me-2"></i>
    Warning message here
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>

<!-- Info Alert -->
<div class="alert alert-admin-info alert-dismissible fade show">
    <i class="fas fa-info-circle me-2"></i>
    Info message here
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
```

## 🧭 Navigation Components

### Breadcrumbs
```html
<nav class="admin-breadcrumb">
    <a href="<?= base_url('admin') ?>">Admin</a>
    <i class="fas fa-chevron-right"></i>
    <a href="<?= base_url('admin/section') ?>">Section</a>
    <i class="fas fa-chevron-right"></i>
    <span>Current Page</span>
</nav>
```

### Sidebar Navigation
```html
<nav class="admin-nav">
    <a href="#" class="admin-nav-link active">
        <i class="fas fa-tachometer-alt"></i>
        <span>Dashboard</span>
    </a>
    <a href="#" class="admin-nav-link">
        <i class="fas fa-file-alt"></i>
        <span>Pages</span>
        <span class="admin-nav-badge">5</span>
    </a>
</nav>
```

## 🎛️ Utility Components

### Loading States
```html
<!-- Loading Button -->
<button class="btn btn-admin-primary" disabled>
    <i class="fas fa-spinner fa-spin me-2"></i>
    Loading...
</button>

<!-- Loading Overlay -->
<div class="admin-loading-overlay">
    <div class="admin-spinner">
        <i class="fas fa-spinner fa-spin"></i>
    </div>
</div>
```

### Avatar Component
```html
<div class="admin-avatar">
    <img src="avatar.jpg" alt="User Name">
</div>

<!-- With status indicator -->
<div class="admin-avatar admin-avatar-online">
    <img src="avatar.jpg" alt="User Name">
</div>
```

## 📱 Responsive Utilities

### Responsive Classes
```html
<!-- Hide on mobile -->
<div class="d-none d-md-block">Desktop only content</div>

<!-- Show only on mobile -->
<div class="d-block d-md-none">Mobile only content</div>

<!-- Responsive text alignment -->
<div class="text-center text-md-start">Responsive alignment</div>
```

This component library provides all the building blocks needed to create consistent, professional admin interfaces. Each component is designed to work together seamlessly while maintaining flexibility for customization.
