# 🎨 Admin Interface Styling Guide

## 🎯 Overview

This document provides comprehensive guidelines for styling the admin interface. It covers the design system, color palette, typography, spacing, and component styling patterns used throughout the CMS admin interface.

## 🎨 Design System

### Color Palette

#### Primary Colors
```css
:root {
    --admin-primary: #4f46e5;      /* Indigo - Primary actions */
    --admin-secondary: #6b7280;    /* Gray - Secondary actions */
    --admin-success: #10b981;      /* Emerald - Success states */
    --admin-warning: #f59e0b;      /* Amber - Warning states */
    --admin-danger: #ef4444;       /* Red - Danger/error states */
    --admin-info: #3b82f6;         /* Blue - Info states */
}
```

#### Neutral Colors
```css
:root {
    --admin-white: #ffffff;
    --admin-light: #f8fafc;        /* Light backgrounds */
    --admin-lighter: #f1f5f9;      /* Lighter backgrounds */
    --admin-gray-100: #f3f4f6;
    --admin-gray-200: #e5e7eb;
    --admin-gray-300: #d1d5db;
    --admin-gray-400: #9ca3af;
    --admin-gray-500: #6b7280;
    --admin-gray-600: #4b5563;
    --admin-gray-700: #374151;
    --admin-gray-800: #1f2937;
    --admin-gray-900: #111827;
    --admin-dark: #1e293b;         /* Dark text/backgrounds */
}
```

#### Semantic Colors
```css
:root {
    --admin-border: #e2e8f0;       /* Default borders */
    --admin-border-light: #f1f5f9; /* Light borders */
    --admin-text: #1e293b;         /* Primary text */
    --admin-text-muted: #64748b;   /* Secondary text */
    --admin-text-light: #94a3b8;   /* Tertiary text */
}
```

### Shadows and Effects
```css
:root {
    --admin-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --admin-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --admin-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --admin-shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    
    --admin-ring: 0 0 0 3px rgba(79, 70, 229, 0.1);
    --admin-ring-danger: 0 0 0 3px rgba(239, 68, 68, 0.1);
}
```

### Border Radius
```css
:root {
    --admin-radius-sm: 4px;
    --admin-radius: 6px;
    --admin-radius-md: 8px;
    --admin-radius-lg: 12px;
    --admin-radius-xl: 16px;
    --admin-radius-full: 9999px;
}
```

### Spacing System
```css
:root {
    --admin-spacing-xs: 0.25rem;   /* 4px */
    --admin-spacing-sm: 0.5rem;    /* 8px */
    --admin-spacing-md: 1rem;      /* 16px */
    --admin-spacing-lg: 1.5rem;    /* 24px */
    --admin-spacing-xl: 2rem;      /* 32px */
    --admin-spacing-2xl: 3rem;     /* 48px */
    --admin-spacing-3xl: 4rem;     /* 64px */
}
```

## 📝 Typography

### Font Stack
```css
:root {
    --admin-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --admin-font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

body {
    font-family: var(--admin-font-family);
    font-size: 14px;
    line-height: 1.5;
    color: var(--admin-text);
}
```

### Typography Scale
```css
.admin-text-xs { font-size: 0.75rem; line-height: 1rem; }
.admin-text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.admin-text-base { font-size: 1rem; line-height: 1.5rem; }
.admin-text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.admin-text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.admin-text-2xl { font-size: 1.5rem; line-height: 2rem; }
.admin-text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.admin-text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
```

### Heading Styles
```css
.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--admin-text);
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1rem;
    color: var(--admin-text-muted);
    margin-bottom: 0;
}

.admin-card h5 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--admin-text);
}
```

## 🏗️ Layout Components

### Admin Sidebar
```css
.admin-sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: transform 0.3s ease;
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
}
```

### Admin Content Area
```css
.admin-content {
    margin-left: 280px;
    min-height: 100vh;
    background: var(--admin-light);
    transition: margin-left 0.3s ease;
}

@media (max-width: 768px) {
    .admin-content {
        margin-left: 0;
    }
}
```

### Page Header
```css
.page-header {
    background: white;
    padding: 2rem;
    border-bottom: 1px solid var(--admin-border);
    margin-bottom: 2rem;
}

.page-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .page-actions {
        width: 100%;
        justify-content: flex-start;
    }
}
```

## 🎴 Card Components

### Basic Card
```css
.admin-card {
    background: white;
    border-radius: var(--admin-radius-lg);
    box-shadow: var(--admin-shadow);
    border: 1px solid var(--admin-border);
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}

.admin-card:hover {
    box-shadow: var(--admin-shadow-md);
}
```

### Card Header
```css
.admin-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    background: var(--admin-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.admin-card-header h5 {
    margin: 0;
    font-weight: 600;
    color: var(--admin-text);
}
```

### Card Body
```css
.admin-card-body {
    padding: 1.5rem;
}

.admin-card-body:last-child {
    padding-bottom: 1.5rem;
}
```

### Card Footer
```css
.admin-card-footer {
    padding: 1rem 1.5rem;
    background: var(--admin-light);
    border-top: 1px solid var(--admin-border);
}
```

## 📝 Form Components

### Form Groups
```css
.admin-form-group {
    margin-bottom: 1.5rem;
}

.admin-form-group:last-child {
    margin-bottom: 0;
}
```

### Form Labels
```css
.admin-form-label {
    display: block;
    font-weight: 500;
    color: var(--admin-text);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.admin-form-label .text-danger {
    color: var(--admin-danger);
}
```

### Form Controls
```css
.admin-form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-radius);
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--admin-text);
    background: white;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.admin-form-control:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: var(--admin-ring);
}

.admin-form-control.is-invalid {
    border-color: var(--admin-danger);
    box-shadow: var(--admin-ring-danger);
}

.admin-form-control.is-valid {
    border-color: var(--admin-success);
}
```

### Form Text
```css
.admin-form-text {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    margin-top: 0.25rem;
}
```

## 📊 Table Components

### Basic Table
```css
.admin-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.admin-table th,
.admin-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--admin-border);
}

.admin-table th {
    background: var(--admin-light);
    font-weight: 600;
    color: var(--admin-text);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.admin-table tbody tr:hover {
    background: var(--admin-lighter);
}
```

### Responsive Table
```css
.admin-table-responsive {
    overflow-x: auto;
    border-radius: var(--admin-radius-lg);
    border: 1px solid var(--admin-border);
}

@media (max-width: 768px) {
    .admin-table-responsive {
        font-size: 0.75rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 0.5rem;
    }
}
```

### Table Actions
```css
.admin-table-actions {
    width: 1%;
    white-space: nowrap;
    text-align: right;
}

.admin-table-actions .btn-group {
    display: flex;
    gap: 0.25rem;
}
```

## 🔘 Button Components

### Button Base
```css
.btn-admin-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: var(--admin-radius);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 0.5rem;
}

.btn-admin-base:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
```

### Button Variants
```css
.btn-admin-primary {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
}

.btn-admin-primary:hover:not(:disabled) {
    background: #4338ca;
    border-color: #4338ca;
}

.btn-admin-secondary {
    background: var(--admin-secondary);
    color: white;
    border-color: var(--admin-secondary);
}

.btn-admin-success {
    background: var(--admin-success);
    color: white;
    border-color: var(--admin-success);
}

.btn-admin-danger {
    background: var(--admin-danger);
    color: white;
    border-color: var(--admin-danger);
}
```

### Button Sizes
```css
.btn-admin-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-admin-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}
```

## 🏷️ Badge Components

```css
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: var(--admin-radius-full);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.bg-admin-success {
    background: var(--admin-success);
    color: white;
}

.bg-admin-warning {
    background: var(--admin-warning);
    color: white;
}

.bg-admin-danger {
    background: var(--admin-danger);
    color: white;
}
```

## 🚫 Empty State Components

```css
.admin-empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--admin-text-muted);
}

.admin-empty-state i {
    font-size: 3rem;
    color: var(--admin-text-light);
    margin-bottom: 1rem;
}

.admin-empty-state h4 {
    color: var(--admin-text);
    margin-bottom: 0.5rem;
}

.admin-empty-state p {
    margin-bottom: 1.5rem;
}
```

## 📱 Responsive Design

### Breakpoints
```css
/* Mobile First Approach */
/* xs: 0px and up */
/* sm: 576px and up */
/* md: 768px and up */
/* lg: 992px and up */
/* xl: 1200px and up */
/* xxl: 1400px and up */
```

### Mobile Optimizations
```css
@media (max-width: 768px) {
    .admin-content {
        padding: 1rem;
    }
    
    .page-header {
        padding: 1rem;
    }
    
    .admin-card-body {
        padding: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        border-radius: var(--admin-radius);
        margin-bottom: 0.25rem;
    }
}
```

## 🎨 Utility Classes

### Text Colors
```css
.text-admin-primary { color: var(--admin-primary); }
.text-admin-secondary { color: var(--admin-secondary); }
.text-admin-success { color: var(--admin-success); }
.text-admin-warning { color: var(--admin-warning); }
.text-admin-danger { color: var(--admin-danger); }
.text-admin-info { color: var(--admin-info); }
.text-admin-muted { color: var(--admin-text-muted); }
```

### Background Colors
```css
.bg-admin-primary { background-color: var(--admin-primary); }
.bg-admin-light { background-color: var(--admin-light); }
.bg-admin-white { background-color: var(--admin-white); }
```

### Spacing Utilities
```css
.admin-mb-0 { margin-bottom: 0; }
.admin-mb-1 { margin-bottom: var(--admin-spacing-sm); }
.admin-mb-2 { margin-bottom: var(--admin-spacing-md); }
.admin-mb-3 { margin-bottom: var(--admin-spacing-lg); }
.admin-mb-4 { margin-bottom: var(--admin-spacing-xl); }
```

This styling guide provides the foundation for maintaining consistent, professional design across all admin interface components. All styles use CSS variables for easy theming and customization.
